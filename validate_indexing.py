#!/usr/bin/env python3
"""
Script to validate file indexing for SuperMCP analysis
"""

import sys
import os
import requests
import json
from pathlib import Path

def get_analysis_data(analysis_id=1):
    """Get analysis data from the API"""
    try:
        response = requests.get(f"http://localhost:8000/api/v1/analysis/{analysis_id}")
        if response.status_code == 200:
            return response.json()
        else:
            print(f"Error fetching analysis: {response.status_code}")
            return None
    except Exception as e:
        print(f"Error connecting to API: {e}")
        return None

def get_indexing_status(analysis_id=1):
    """Get indexing status from the API"""
    try:
        response = requests.get(f"http://localhost:8000/api/v1/analysis/{analysis_id}/indexing-status")
        if response.status_code == 200:
            return response.json()
        else:
            print(f"Error fetching indexing status: {response.status_code}")
            return None
    except Exception as e:
        print(f"Error connecting to API: {e}")
        return None

def analyze_file_filtering():
    """Analyze which files should be indexed based on the filtering logic"""
    
    # Get the actual repository files
    repo_path = Path("/Users/<USER>/repos/supermcp")
    
    # File extensions to index (from indexing_service.py)
    indexable_extensions = {
        '.py', '.js', '.ts', '.jsx', '.tsx', '.java', '.go', '.rs', '.cpp', '.c', '.h',
        '.php', '.rb', '.swift', '.kt', '.scala', '.clj', '.hs', '.ml', '.r', '.sql',
        '.yaml', '.yml', '.json', '.xml', '.toml', '.ini', '.cfg', '.conf', '.md',
        '.vue', '.svelte', '.dart', '.lua', '.perl', '.sh', '.bash', '.zsh'
    }
    
    # Files to skip
    skip_patterns = [
        'node_modules/', 'venv/', '.git/', '__pycache__/', '.pytest_cache/',
        'dist/', 'build/', 'target/', '.next/', '.nuxt/', 'coverage/',
        'package-lock.json', 'yarn.lock', 'poetry.lock', 'Pipfile.lock',
        '.min.js', '.min.css', 'bundle.js', 'vendor.js'
    ]
    
    # Priority files
    priority_patterns = [
        'main.', 'index.', 'app.', 'server.', 'config.', 'settings.',
        'README', 'package.json', 'requirements.txt', 'Cargo.toml',
        'go.mod', 'pom.xml', 'build.gradle'
    ]
    
    all_files = []
    indexable_files = []
    priority_files = []
    skipped_files = []
    
    # Walk through the repository
    for root, dirs, files in os.walk(repo_path):
        # Skip hidden directories and common build directories
        dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['node_modules', 'venv', '__pycache__', 'dist', 'build', 'target']]
        
        for file in files:
            if file.startswith('.'):
                continue
                
            file_path = Path(root) / file
            relative_path = file_path.relative_to(repo_path)
            
            all_files.append(str(relative_path))
            
            # Check if should be skipped
            if any(pattern in str(relative_path) for pattern in skip_patterns):
                skipped_files.append(str(relative_path))
                continue
            
            # Check file extension
            file_ext = file_path.suffix.lower()
            if file_ext not in indexable_extensions:
                skipped_files.append(str(relative_path))
                continue
            
            # Check if it's a priority file
            file_name = file.lower()
            if any(pattern.lower() in file_name for pattern in priority_patterns):
                priority_files.append(str(relative_path))
            
            # Check file size
            try:
                file_size = file_path.stat().st_size
                if file_size > 500000:  # 500KB limit
                    skipped_files.append(f"{relative_path} (too large: {file_size} bytes)")
                    continue
            except:
                continue
            
            indexable_files.append(str(relative_path))
    
    return {
        'all_files': all_files,
        'indexable_files': indexable_files,
        'priority_files': priority_files,
        'skipped_files': skipped_files
    }

def main():
    print("🔍 SuperMCP File Indexing Validation")
    print("=" * 50)
    
    # Get analysis data
    print("📊 Fetching analysis data...")
    analysis = get_analysis_data()
    if not analysis:
        print("❌ Could not fetch analysis data")
        return
    
    print(f"Repository: {analysis['repo_owner']}/{analysis['repo_name']}")
    print(f"Status: {analysis['status']}")
    
    # Get indexing status
    print("\n📈 Fetching indexing status...")
    indexing = get_indexing_status()
    if indexing:
        print(f"Indexing Status: {indexing['indexing_status']}")
        print(f"Files Indexed: {indexing['files_indexed']}")
        print(f"Total Chunks: {indexing['total_chunks']}")
        print(f"Last Indexed: {indexing['last_indexed_at']}")
    
    # Analyze local repository files
    print(f"\n📁 Analyzing local repository files...")
    file_analysis = analyze_file_filtering()
    
    print(f"Total files in repo: {len(file_analysis['all_files'])}")
    print(f"Indexable files: {len(file_analysis['indexable_files'])}")
    print(f"Priority files: {len(file_analysis['priority_files'])}")
    print(f"Skipped files: {len(file_analysis['skipped_files'])}")
    
    print(f"\n📋 Priority files that should be indexed:")
    for i, file_path in enumerate(file_analysis['priority_files'][:10], 1):
        print(f"  {i:2d}. {file_path}")
    
    print(f"\n📋 Sample indexable files:")
    for i, file_path in enumerate(file_analysis['indexable_files'][:15], 1):
        print(f"  {i:2d}. {file_path}")
    
    print(f"\n🚫 Sample skipped files:")
    for i, file_path in enumerate(file_analysis['skipped_files'][:10], 1):
        print(f"  {i:2d}. {file_path}")
    
    # Compare with actual indexing
    if indexing:
        expected_files = len(file_analysis['indexable_files'])
        actual_files = indexing['files_indexed']
        
        print(f"\n📊 Comparison:")
        print(f"Expected indexable files: {expected_files}")
        print(f"Actually indexed files: {actual_files}")
        
        if actual_files < expected_files:
            print(f"⚠️  {expected_files - actual_files} files may not have been indexed")
        elif actual_files > expected_files:
            print(f"ℹ️  {actual_files - expected_files} more files indexed than expected")
        else:
            print("✅ File count matches expectations")

if __name__ == "__main__":
    main()
