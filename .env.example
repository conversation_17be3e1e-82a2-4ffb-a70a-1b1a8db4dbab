# =============================================================================
# SuperMCP Configuration
# =============================================================================

# Environment (development/production)
ENVIRONMENT=development
DEBUG=true
NODE_ENV=development

# =============================================================================
# Database Configuration
# =============================================================================
POSTGRES_DB=supermcp
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_PORT=5432

# Database URL for SQLAlchemy
DATABASE_URL=***********************************************/supermcp

# =============================================================================
# Redis Configuration
# =============================================================================
REDIS_PORT=6379
REDIS_URL=redis://supermcp-redis:6379/0

# =============================================================================
# Celery Configuration
# =============================================================================
CELERY_BROKER_URL=redis://supermcp-redis:6379/0
CELERY_RESULT_BACKEND=redis://supermcp-redis:6379/0
CELERY_LOG_LEVEL=info
CELERY_CONCURRENCY=2

# =============================================================================
# Application Ports
# =============================================================================
BACKEND_PORT=8000
FRONTEND_PORT=3000
FLOWER_PORT=5555

# =============================================================================
# Docker Configuration
# =============================================================================
RESTART_POLICY=no
BACKEND_DOCKERFILE=Dockerfile
FRONTEND_DOCKERFILE=Dockerfile

# =============================================================================
# GitHub OAuth (REPLACE WITH YOUR VALUES)
# =============================================================================
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret
GITHUB_REDIRECT_URL=http://localhost:3000/auth/callback

# =============================================================================
# JWT & Security (FOR DEVELOPMENT ONLY - GENERATE SECURE KEYS FOR PRODUCTION)
# =============================================================================
JWT_SECRET=4f8b2c9e1a7d3f6b8e2c5a9f1d4e7b3c9f2e5a8d1c4f7b9e2a5d8c1f4e7b3c6a9
SECRET_KEY=SuperMCP#2025

# =============================================================================
# External APIs (Optional)
# =============================================================================
TAVILY_API_KEY=your_tavily_api_key
CONTEXT7_MCP_URL=your_context7_mcp_url

# =============================================================================
# AI APIs for repository analysis (choose one or both)
# =============================================================================
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key

# =============================================================================
# CORS Configuration
# =============================================================================
CORS_ORIGINS=["http://localhost:3000","http://localhost:3001"]

# =============================================================================
# Frontend Configuration
# =============================================================================
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_APP_NAME=SuperMCP

# =============================================================================
# Production-only Configuration (uncomment for production)
# =============================================================================
# ENVIRONMENT=production
# DEBUG=false
# NODE_ENV=production
# RESTART_POLICY=unless-stopped
# BACKEND_DOCKERFILE=Dockerfile.prod
# FRONTEND_DOCKERFILE=Dockerfile.prod
# POSTGRES_PASSWORD=your_secure_password_here
# JWT_SECRET=your_secure_jwt_secret_here
# SECRET_KEY=your_secure_secret_key_here
# REDIS_AUTH_CMD=--requirepass your_redis_password
# HTTP_PORT=80
# HTTPS_PORT=443
# FLOWER_USER=admin
# FLOWER_PASSWORD=secure_password