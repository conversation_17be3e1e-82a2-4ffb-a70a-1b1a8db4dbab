<div align="center">
  <img src="supermcp.png" alt="SuperMCP Logo" width="200" height="200">

  # SuperMCP

  **Open source tool to analyze GitHub repositories and generate MCP (Model Context Protocol) servers with AI-powered analysis.**

  [![MIT License](https://img.shields.io/badge/License-MIT-green.svg)](https://choosealicense.com/licenses/mit/)
  [![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://docker.com)
  [![FastAPI](https://img.shields.io/badge/FastAPI-Backend-green.svg)](https://fastapi.tiangolo.com)
  [![Next.js](https://img.shields.io/badge/Next.js-Frontend-black.svg)](https://nextjs.org)
</div>

## Overview

SuperMCP is a comprehensive, open source tool that analyzes GitHub repositories to:
- Assess the feasibility of creating MCP (Model Context Protocol) servers
- Generate production-ready MCP server implementations
- Provide detailed implementation recommendations and guides
- Support multiple programming languages (Python, TypeScript, Go, Rust, Java, C#)

## Architecture

<div align="center">
  <img src="SuperMCP_arch.png" alt="SuperMCP Architecture" width="800">
  <p><em>SuperMCP system architecture showing the complete analysis and generation pipeline</em></p>
</div>

## Features

### 🔍 **Repository Analysis & Discovery**
- **Multi-language Analysis**: Support for Python, JavaScript/TypeScript, Go, Java, Rust, C#, and more
- **Repository Browser**: Browse and select repositories directly from your GitHub account
- **Smart URL Parsing**: Automatic repository URL validation and parsing
- **Real-time Processing**: Background analysis with live status updates and progress tracking

### 🧠 **AI-Powered Intelligence**
- **MCP Feasibility Scoring**: Intelligent scoring system (0-100) based on code structure, APIs, and dependencies
- **AI-Powered Code Generation**: Generate complete MCP servers with implementation guides
- **Dynamic MCP Discovery**: Real-time search for existing MCP servers using Tavily API
- **Integration Detection**: Automatic detection of third-party services that could be replaced with MCPs

### 🔐 **Security & Authentication**
- **GitHub OAuth Integration**: Secure authentication with repository access
- **JWT Token Management**: Secure session management with automatic token refresh
- **Private Repository Support**: Full access to private repositories with proper authentication

### 📊 **Comprehensive Reporting**
- **Detailed Analysis Results**: In-depth reports with feasibility scores and recommendations
- **Dependency Analysis**: Complete dependency tree analysis with MCP potential assessment
- **API Endpoint Detection**: Automatic discovery of REST APIs, GraphQL endpoints, and CLI interfaces
- **Implementation Guides**: Step-by-step guides for MCP server implementation

### 🎨 **Modern User Experience**
- **Clean, Professional UI**: Modern interface designed for developers using shadcn/ui components
- **Responsive Design**: Works seamlessly on desktop and mobile devices
- **Real-time Updates**: Live progress tracking and status updates during analysis
- **Error Handling**: Comprehensive error handling with helpful user feedback

### 🔓 **Open Source & Deployment**
- **MIT License**: Completely open source - deploy and customize your own instance
- **Docker Ready**: Complete Docker Compose setup for easy deployment
- **Environment Configuration**: Flexible configuration via environment variables
- **Production Ready**: Includes production deployment configurations with Nginx and SSL

## 🆕 Latest Updates

### Repository Browser Feature (v1.2.0)
- **✅ Interactive Repository Browser**: Browse and select repositories directly from your GitHub account
- **✅ Smart Search & Filtering**: Real-time search through repository names and descriptions
- **✅ Visual Repository Cards**: Rich repository information including language, stars, and privacy status
- **✅ One-Click Selection**: Auto-populate repository URL with visual confirmation
- **✅ Authentication Integration**: Seamless GitHub OAuth integration with proper error handling

### Enhanced User Experience
- **✅ Improved Error Handling**: Clear error messages with actionable solutions
- **✅ Loading States**: Professional loading indicators and progress feedback
- **✅ Responsive Design**: Optimized for all screen sizes and devices
- **✅ Clean UI Components**: Modern design using shadcn/ui component library

### Technical Improvements
- **✅ API Optimization**: Fixed repository API endpoints and improved error handling
- **✅ Code Cleanup**: Removed test scripts and improved code organization
- **✅ Docker Integration**: All services now run properly in Docker containers
- **✅ Authentication Flow**: Enhanced JWT token management and GitHub OAuth integration

## Tech Stack

### Backend
- **Framework**: FastAPI
- **Database**: PostgreSQL with SQLAlchemy ORM
- **Task Queue**: Celery with Redis
- **Authentication**: GitHub OAuth + JWT

### Frontend
- **Framework**: Next.js 14 with TypeScript
- **UI Library**: shadcn/ui components
- **Styling**: Tailwind CSS with clean, modern design

### Infrastructure
- **Development**: Docker Compose
- **Database**: PostgreSQL 15
- **Cache/Queue**: Redis 7

## Quick Start

### 🚀 Fresh Development Environment

**Complete Reset (recommended for first setup):**
```bash
./reset-dev-environment.sh
```

**Database Only Reset (faster, keeps containers running):**
```bash
./cleanup-database.sh
```

The complete reset will:
- � Stop all Docker services
- �️ Remove containers, volumes, and networks
- � Start fresh services
- �️ Run database migrations

The database reset will:
- �️ Clean all database tables
- �️ Run database migrations
- 🧹 Clear Redis cache

After running either script, visit `http://localhost:3000` and sign up with your GitHub account.

### 🐳 **Unified Docker Configuration**

We use a single `docker-compose.yml` file that works for both development and production:

**Development (default):**
```bash
docker-compose up -d
```

**Production:**
```bash
# Copy and customize production config
cp .env.example .env
# Edit .env with your production values (see production section in .env.example)
docker-compose --profile production up -d
```

**With monitoring (Flower):**
```bash
docker-compose --profile monitoring up -d
```

### Prerequisites
- Docker and Docker Compose
- Node.js 18+ (for local frontend development)
- Python 3.11+ (for local backend development)

### Development Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/surenganne/supermcp.git
   cd supermcp
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start the development environment**
   ```bash
   docker-compose up -d
   ```

4. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/api/v1/docs
   - Flower (Celery monitoring): http://localhost:5555 (with `--profile monitoring`)

5. **Start analyzing repositories**
   - Sign up/Login with your GitHub account
   - Use the Repository Browser to select a repository or enter a GitHub URL manually
   - Start the analysis and monitor progress in real-time
   - Review detailed results and generate MCP servers

## Configuration

### GitHub OAuth Setup

1. Go to GitHub Settings > Developer settings > OAuth Apps
2. Create a new OAuth App with:
   - Application name: SuperMCP
   - Homepage URL: http://localhost:3000
   - Authorization callback URL: http://localhost:3000/auth/callback
3. Copy the Client ID and Client Secret to your `.env` file

### External Services (Required for MCP Discovery)

- **Tavily API**: Sign up at tavily.com for real-time MCP server discovery
- **Context7 MCP**: Configure your Context7 MCP server URL for documentation search

**Important**: SuperMCP uses real-time search to discover MCP servers dynamically. Set up these API keys to enable MCP recommendations.

## 📁 Repository Browser

SuperMCP includes an intuitive repository browser that makes it easy to select repositories for analysis:

### Features
- **🔍 Browse Your Repositories**: View all your GitHub repositories in a clean, organized interface
- **🔎 Smart Search**: Real-time search through repository names and descriptions
- **📊 Repository Information**: See language, stars, privacy status, and last updated date
- **🎯 One-Click Selection**: Click any repository to automatically populate the analysis form
- **✅ Visual Feedback**: Clear indication when a repository is selected from the browser
- **🔐 Authentication Aware**: Seamless integration with GitHub OAuth authentication

### How to Use
1. **Navigate to New Analysis**: Go to the "New Analysis" page
2. **Expand Repository Browser**: Click "▼ Browse" to open the repository browser
3. **Search (Optional)**: Use the search box to filter repositories
4. **Select Repository**: Click on any repository card to select it
5. **Start Analysis**: The repository URL will be auto-populated - click "Start Analysis"

### Authentication
The repository browser requires GitHub authentication to access your repositories. If you're not logged in, you'll see a helpful message with a login button.

## API Documentation

The API documentation is automatically generated and available at:
- Development: http://localhost:8000/docs
- Interactive API explorer with request/response examples

## Project Structure

```
supermcp/
├── backend/           # FastAPI backend application
├── frontend/          # Next.js frontend application
├── docker-compose.yml # Unified Docker Compose configuration
├── .env               # Environment variables (working copy)
├── .env.example       # Environment template with all options
├── reset-dev-environment.sh # Complete environment reset
├── cleanup-database.sh      # Database-only cleanup
└── README.md          # Project documentation
```

## UI/UX Design

SuperMCP features a clean, modern interface designed for professional developers:

- **Streamlined Authentication**: Direct login flow without unnecessary intermediate pages
- **Clean Dashboard**: Focus on essential information and actions
- **Professional Design**: Minimal use of gradients and animations for a business-ready appearance
- **Consistent Layout**: Unified design language across all pages
- **Responsive Design**: Works seamlessly on desktop and mobile devices

## Development Scripts

### 🧹 `reset-dev-environment.sh`
Complete development environment reset script:
- Stops and removes all Docker containers/volumes
- Starts fresh services with clean database
- Runs database migrations
- **Usage**: `./reset-dev-environment.sh`

### 🗄️ `cleanup-database.sh`
Quick database cleanup without Docker restart:
- Cleans all database tables (preserves schema)
- Runs database migrations
- Clears Redis cache
- **Usage**: `./cleanup-database.sh`

## Open Source & Customization

SuperMCP is open source (MIT license) and designed for easy customization:

- **Environment Configuration**: Update API keys and settings via environment variables
- **Custom Deployment**: Deploy to your own infrastructure
- **UI Customization**: Modify the clean, professional design to match your brand
- **Feature Extensions**: Add new analysis capabilities or integrations

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For questions, issues, or feature requests, please open an issue on GitHub.

---

**Built with ❤️ using FastAPI, Next.js, and the power of MCP servers**