'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useAuth } from '@/hooks/use-auth'
import {
    ArrowRight,
    Brain,
    CheckCircle,
    Code,
    Github,
    Zap
} from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'

export default function Home() {
  const { isAuthenticated, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (isAuthenticated && !loading) {
      router.push('/dashboard')
    }
  }, [isAuthenticated, loading, router])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  if (isAuthenticated) {
    return null // Will redirect
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className="border-b border-gray-200 bg-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-3">
              <img
                src="/supermcp.png"
                alt="SuperMCP Logo"
                className="h-8 w-8 object-contain"
              />
              <span className="text-xl font-semibold text-gray-900">
                Super<span className="text-primary">MCP</span>
              </span>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/auth/login">
                <Button variant="ghost" className="text-gray-600 hover:text-gray-900">
                  Sign In
                </Button>
              </Link>
              <Link href="/auth/login">
                <Button className="bg-primary hover:bg-primary/90 text-white">
                  <Github className="w-4 h-4 mr-2" />
                  Get Started
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Transform GitHub Repositories
              <br />
              <span className="text-primary">Into Intelligent MCP Servers</span>
            </h1>

            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              AI-powered analysis that understands your codebase and generates production-ready
              MCP servers with <span className="font-semibold text-gray-800">real business logic</span>,
              marketplace intelligence, and native language matching.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <Link href="/auth/login">
                <Button size="lg" className="bg-primary hover:bg-primary/90 text-white px-8 py-3">
                  <Github className="w-5 h-5 mr-2" />
                  Start Analysis
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
              </Link>
              <Button variant="outline" size="lg" className="px-8 py-3">
                Learn More
              </Button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-2xl mx-auto">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900 mb-1">Smart</div>
                <div className="text-gray-600">Language Matching</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900 mb-1">Claude</div>
                <div className="text-gray-600">AI Powered</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900 mb-1">Real</div>
                <div className="text-gray-600">Business Logic</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Intelligent MCP Generation
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Beyond basic code generation - we understand your business logic and create functional MCP servers
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <Card className="border border-gray-200">
              <CardHeader>
                <div className="h-10 w-10 bg-primary rounded-lg flex items-center justify-center mb-3">
                  <Brain className="h-5 w-5 text-white" />
                </div>
                <CardTitle className="text-lg">Repository Understanding</CardTitle>
                <CardDescription>
                  Deep codebase analysis that understands your business logic, API endpoints, and core functionality.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-center">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                    Business logic extraction
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                    API endpoint mapping
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                    Native language detection
                  </li>
                </ul>
              </CardContent>
            </Card>

            <Card className="border border-gray-200">
              <CardHeader>
                <div className="h-10 w-10 bg-green-600 rounded-lg flex items-center justify-center mb-3">
                  <Zap className="h-5 w-5 text-white" />
                </div>
                <CardTitle className="text-lg">Strategic Recommendations</CardTitle>
                <CardDescription>
                  Marketplace-informed suggestions with existing MCP servers and custom development guidance.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-center">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                    MCP marketplace integration
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                    User-driven suggestions
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                    Implementation roadmaps
                  </li>
                </ul>
              </CardContent>
            </Card>

            <Card className="border border-gray-200">
              <CardHeader>
                <div className="h-10 w-10 bg-purple-600 rounded-lg flex items-center justify-center mb-3">
                  <Code className="h-5 w-5 text-white" />
                </div>
                <CardTitle className="text-lg">Functional MCP Servers</CardTitle>
                <CardDescription>
                  Generate working MCP servers with real business logic, not just templates.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-center">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                    Real business logic
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                    Native language matching
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                    Production-ready code
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Differentiators Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Why SuperMCP is Different
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              We don't just generate template code - we create intelligent, functional MCP servers
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <div className="bg-white rounded-lg p-6 border border-gray-200">
              <div className="flex items-start space-x-4">
                <div className="h-8 w-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Brain className="h-4 w-4 text-blue-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Understands Your Business Logic</h3>
                  <p className="text-gray-600 text-sm">
                    Extracts actual functions, API endpoints, and business rules from your codebase to create meaningful MCP tools.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg p-6 border border-gray-200">
              <div className="flex items-start space-x-4">
                <div className="h-8 w-8 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Zap className="h-4 w-4 text-green-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Native Language Matching</h3>
                  <p className="text-gray-600 text-sm">
                    Java codebase gets Java MCP servers, Go gets Go, ensuring optimal team integration and deployment.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg p-6 border border-gray-200">
              <div className="flex items-start space-x-4">
                <div className="h-8 w-8 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Code className="h-4 w-4 text-purple-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Marketplace Intelligence</h3>
                  <p className="text-gray-600 text-sm">
                    Recommends existing MCP servers from the marketplace and identifies when custom development is needed.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg p-6 border border-gray-200">
              <div className="flex items-start space-x-4">
                <div className="h-8 w-8 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <CheckCircle className="h-4 w-4 text-orange-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Claude-Powered Analysis</h3>
                  <p className="text-gray-600 text-sm">
                    Uses Claude 3.5 Sonnet for deep code understanding and generates MCP servers optimized for Claude integration.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-primary">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            Ready to Create Intelligent MCP Servers?
          </h2>
          <p className="text-xl text-primary-foreground/80 mb-8 max-w-2xl mx-auto">
            Stop building template MCP servers. Generate functional, business-logic-aware MCP servers that understand your codebase.
          </p>

          <Link href="/auth/login">
            <Button size="lg" className="bg-white text-primary hover:bg-gray-100 px-8 py-3">
              <Github className="w-5 h-5 mr-2" />
              Start Analysis
              <ArrowRight className="w-5 h-5 ml-2" />
            </Button>
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 bg-gray-900">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="flex items-center space-x-3 mb-4 md:mb-0">
              <img
                src="/supermcp.png"
                alt="SuperMCP Logo"
                className="h-8 w-8 object-contain"
              />
              <span className="text-xl font-semibold text-white">
                Super<span className="text-primary">MCP</span>
              </span>
            </div>

            <div className="flex items-center space-x-6 text-gray-400 text-sm">
              <Link href="#" className="hover:text-white">
                Privacy Policy
              </Link>
              <Link href="#" className="hover:text-white">
                Terms of Service
              </Link>
              <Link href="#" className="hover:text-white">
                Documentation
              </Link>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400 text-sm">
            <p>&copy; 2024 SuperMCP. Open source MIT license.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}