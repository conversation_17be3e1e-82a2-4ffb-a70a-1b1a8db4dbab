'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Github, Loader2 } from 'lucide-react'
import { useAuth } from '@/hooks/use-auth'

interface LoginButtonProps {
  size?: 'default' | 'sm' | 'lg'
  variant?: 'default' | 'outline' | 'ghost'
  className?: string
}

export function LoginButton({ size = 'default', variant = 'default', className }: LoginButtonProps) {
  const { login, loading } = useAuth()

  return (
    <Button
      onClick={login}
      disabled={loading}
      size={size}
      variant={variant}
      className={className}
    >
      {loading ? (
        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
      ) : (
        <Github className="w-4 h-4 mr-2" />
      )}
      Sign in with GitHub
    </Button>
  )
}