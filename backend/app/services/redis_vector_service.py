"""
Redis Vector Database Service

Handles code indexing and retrieval using Redis with RedisSearch and Vector Similarity Search.
Provides semantic search capabilities for better code understanding.
"""

import json
import logging
from typing import Dict, List, Any
from datetime import datetime
import redis
from ..config import settings

logger = logging.getLogger(__name__)


class RedisVectorService:
    """Service for managing code indexing in Redis vector database"""
    
    def __init__(self):
        self.client = None
        self.key_prefix = "code_chunk:"
        self.metadata_key = "code_chunks_metadata:"
        self.max_chunk_size = 1000
        
    async def __aenter__(self):
        """Async context manager entry"""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.client:
            self.client.close()
    
    async def connect(self):
        """Connect to Redis instance"""
        try:
            # Connect to Redis using environment configuration
            import os

            # Try to use REDIS_URL first, fallback to individual components
            redis_url = os.getenv('REDIS_URL', 'redis://supermcp-redis:6379/0')

            # Parse Redis URL to get components
            if redis_url.startswith('redis://'):
                # Extract host and port from URL
                url_parts = redis_url.replace('redis://', '').split('/')
                host_port = url_parts[0].split(':')
                redis_host = host_port[0]
                redis_port = int(host_port[1]) if len(host_port) > 1 else 6379
            else:
                redis_host = os.getenv('REDIS_HOST', 'supermcp-redis')
                redis_port = int(os.getenv('REDIS_PORT', '6379'))

            self.client = redis.Redis(
                host=redis_host,
                port=redis_port,
                db=2,  # Use db=2 for vector storage (separate from Celery)
                decode_responses=True,
                socket_connect_timeout=10,
                socket_timeout=10,
                password=None
            )

            # Test connection
            self.client.ping()

            logger.info("Connected to Redis vector database successfully")

        except Exception as e:
            logger.error(f"Failed to connect to Redis: {str(e)}")
            raise Exception(f"Redis connection failed: {str(e)}")
    
    async def index_repository_code(self, analysis_id: int, repo_content: Dict[str, Any]) -> Dict[str, Any]:
        """
        Index repository code into Redis
        
        Args:
            analysis_id: ID of the analysis
            repo_content: Repository content from analysis service
            
        Returns:
            Indexing result with metadata
        """
        try:
            logger.info(f"Starting Redis indexing for analysis {analysis_id}")
            
            # Get code samples
            code_samples = repo_content.get("code_samples", {})
            indexable_files = self._filter_indexable_files(code_samples)
            
            if not indexable_files:
                return {
                    "status": "completed",
                    "files_indexed": 0,
                    "chunks_created": 0,
                    "vector_db_id": f"analysis_{analysis_id}"
                }
            
            # Clear existing data for this analysis
            await self._clear_analysis_data(analysis_id)
            
            # Index files
            total_chunks = 0

            for file_path, content in indexable_files.items():
                chunks = self._create_chunks(analysis_id, file_path, content)

                # Index chunks
                if chunks:
                    pipe = self.client.pipeline()
                    for chunk in chunks:
                        key = f"{self.key_prefix}{analysis_id}:{chunk['chunk_index']}:{hash(chunk['file_path'])}"

                        # Store chunk data as JSON
                        chunk_data = {
                            "analysis_id": chunk["analysis_id"],
                            "file_path": chunk["file_path"],
                            "chunk_index": chunk["chunk_index"],
                            "content": chunk["content"],
                            "start_char": chunk["start_char"],
                            "end_char": chunk["end_char"],
                            "language": chunk["language"],
                            "created_at": datetime.utcnow().isoformat()
                        }

                        # Store as JSON string
                        pipe.set(key, json.dumps(chunk_data))

                        # Add to metadata set for easy retrieval
                        pipe.sadd(f"{self.metadata_key}{analysis_id}", key)

                    pipe.execute()
                    total_chunks += len(chunks)
                    logger.debug(f"Indexed {len(chunks)} chunks from {file_path}")
            
            result = {
                "status": "completed",
                "files_indexed": len(indexable_files),
                "chunks_created": total_chunks,
                "vector_db_id": f"analysis_{analysis_id}"
            }
            
            logger.info(f"Redis indexing completed for analysis {analysis_id}: {len(indexable_files)} files, {total_chunks} chunks")
            return result
            
        except Exception as e:
            logger.error(f"Error indexing repository code: {str(e)}")
            raise Exception(f"Redis indexing failed: {str(e)}")
    
    async def search_similar_code(self, analysis_id: int, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Search for similar code chunks using simple text matching

        Args:
            analysis_id: ID of the analysis to search within
            query: Search query
            limit: Maximum number of results

        Returns:
            List of similar code chunks
        """
        try:
            # Get all chunk keys for this analysis
            chunk_keys = self.client.smembers(f"{self.metadata_key}{analysis_id}")

            results = []
            query_lower = query.lower()

            for key in chunk_keys:
                chunk_data_str = self.client.get(key)
                if chunk_data_str:
                    chunk_data = json.loads(chunk_data_str)

                    # Simple text matching
                    if query_lower in chunk_data.get("content", "").lower():
                        results.append({
                            "file_path": chunk_data.get("file_path", ""),
                            "content": chunk_data.get("content", ""),
                            "chunk_index": chunk_data.get("chunk_index", 0),
                            "start_char": chunk_data.get("start_char", 0),
                            "end_char": chunk_data.get("end_char", 0),
                            "language": chunk_data.get("language", ""),
                            "score": 1.0  # Simple scoring
                        })

                        if len(results) >= limit:
                            break

            return results

        except Exception as e:
            logger.error(f"Error searching similar code: {str(e)}")
            return []
    
    async def get_analysis_stats(self, analysis_id: int) -> Dict[str, Any]:
        """Get statistics about indexed code for an analysis"""
        try:
            # Get all chunk keys for this analysis
            chunk_keys = self.client.smembers(f"{self.metadata_key}{analysis_id}")

            total_chunks = len(chunk_keys)
            unique_files = set()

            for key in chunk_keys:
                chunk_data_str = self.client.get(key)
                if chunk_data_str:
                    chunk_data = json.loads(chunk_data_str)
                    unique_files.add(chunk_data.get("file_path", ""))

            return {
                "total_chunks": total_chunks,
                "unique_files": len(unique_files),
                "files": list(unique_files)
            }

        except Exception as e:
            logger.error(f"Error getting analysis stats: {str(e)}")
            return {"total_chunks": 0, "unique_files": 0, "files": []}
    
    async def _clear_analysis_data(self, analysis_id: int):
        """Clear existing data for an analysis"""
        try:
            # Get all chunk keys for this analysis
            chunk_keys = self.client.smembers(f"{self.metadata_key}{analysis_id}")

            if chunk_keys:
                # Delete all chunk data
                pipe = self.client.pipeline()
                for key in chunk_keys:
                    pipe.delete(key)
                # Delete the metadata set
                pipe.delete(f"{self.metadata_key}{analysis_id}")
                pipe.execute()

                logger.debug(f"Cleared {len(chunk_keys)} existing chunks for analysis {analysis_id}")

        except Exception as e:
            logger.warning(f"Error clearing analysis data: {str(e)}")
    

    
    def _filter_indexable_files(self, code_samples: Dict[str, str]) -> Dict[str, str]:
        """Filter files that should be indexed"""
        indexable = {}
        
        # File extensions to index
        indexable_extensions = {
            '.py', '.js', '.ts', '.jsx', '.tsx', '.java', '.go', '.rs', '.cpp', '.c', '.h',
            '.php', '.rb', '.swift', '.kt', '.scala', '.clj', '.hs', '.ml', '.r', '.sql',
            '.yaml', '.yml', '.json', '.xml', '.toml', '.ini', '.cfg', '.conf'
        }
        
        # Files to skip
        skip_patterns = [
            'node_modules/', 'venv/', '.git/', '__pycache__/', '.pytest_cache/',
            'dist/', 'build/', 'target/', '.next/', '.nuxt/', 'coverage/',
            'package-lock.json', 'yarn.lock', 'poetry.lock', 'Pipfile.lock'
        ]
        
        for file_path, content in code_samples.items():
            # Skip if not string content
            if not isinstance(content, str):
                continue
            
            # Skip large files (>100KB)
            if len(content) > 100000:
                continue
            
            # Skip files in excluded directories
            if any(pattern in file_path for pattern in skip_patterns):
                continue
            
            # Check file extension
            file_ext = '.' + file_path.split('.')[-1] if '.' in file_path else ''
            if file_ext.lower() in indexable_extensions:
                indexable[file_path] = content
        
        return indexable
    
    def _create_chunks(self, analysis_id: int, file_path: str, content: str) -> List[Dict[str, Any]]:
        """Create chunks from file content"""
        chunks = []
        
        # Determine language from file extension
        language = self._detect_language(file_path)
        
        # Split content into chunks
        for i in range(0, len(content), self.max_chunk_size):
            chunk_content = content[i:i + self.max_chunk_size]
            
            chunk = {
                "analysis_id": analysis_id,
                "file_path": file_path,
                "chunk_index": len(chunks),
                "content": chunk_content,
                "start_char": i,
                "end_char": min(i + self.max_chunk_size, len(content)),
                "language": language
            }
            
            chunks.append(chunk)
        
        return chunks
    
    def _detect_language(self, file_path: str) -> str:
        """Detect programming language from file extension"""
        ext_to_lang = {
            '.py': 'python',
            '.js': 'javascript',
            '.ts': 'typescript',
            '.jsx': 'javascript',
            '.tsx': 'typescript',
            '.java': 'java',
            '.go': 'go',
            '.rs': 'rust',
            '.cpp': 'cpp',
            '.c': 'c',
            '.h': 'c',
            '.php': 'php',
            '.rb': 'ruby',
            '.swift': 'swift',
            '.kt': 'kotlin',
            '.scala': 'scala',
            '.clj': 'clojure',
            '.hs': 'haskell',
            '.ml': 'ocaml',
            '.r': 'r',
            '.sql': 'sql',
            '.yaml': 'yaml',
            '.yml': 'yaml',
            '.json': 'json',
            '.xml': 'xml',
            '.toml': 'toml',
            '.ini': 'ini',
            '.cfg': 'config',
            '.conf': 'config'
        }
        
        file_ext = '.' + file_path.split('.')[-1] if '.' in file_path else ''
        return ext_to_lang.get(file_ext.lower(), 'text')
