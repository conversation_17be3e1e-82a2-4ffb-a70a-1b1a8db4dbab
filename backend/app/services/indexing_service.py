"""
Code Indexing Service

Handles indexing of repository code for better context understanding.
Provides progress tracking and re-indexing capabilities.
"""

import asyncio
import hashlib
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from sqlalchemy.orm import Session
from ..database import get_db
from ..models.analysis import RepoAnalysis
from .redis_vector_service import RedisVectorService

logger = logging.getLogger(__name__)


class IndexingService:
    """Service for indexing repository code with progress tracking"""
    
    def __init__(self):
        self.chunk_size = 2000  # Increased chunk size for better context
        self.max_file_size = 500000  # Increased to 500KB for larger files
        self.max_total_files = 200  # Increased from 50 to 200 files
        self.chunk_overlap = 200  # Overlap between chunks for better context
    
    async def index_repository(self, analysis_id: int, repo_content: Dict[str, Any]) -> Dict[str, Any]:
        """
        Index repository code with progress tracking
        
        Args:
            analysis_id: ID of the analysis record
            repo_content: Repository content from analysis service
            
        Returns:
            Indexing result with status and metadata
        """
        logger.info(f"Starting code indexing for analysis {analysis_id}")
        
        try:
            # Update status to indexing
            await self._update_indexing_status(analysis_id, "indexing", 0)
            
            # Calculate code hash for change detection
            code_hash = self._calculate_code_hash(repo_content)
            
            # Get files to index
            code_samples = repo_content.get("code_samples", {})
            indexable_files = self._filter_indexable_files(code_samples)
            
            total_files = len(indexable_files)
            if total_files == 0:
                await self._update_indexing_status(analysis_id, "completed", 100)
                return {
                    "status": "completed",
                    "files_indexed": 0,
                    "total_chunks": 0,
                    "code_hash": code_hash
                }
            
            # Index files with progress updates using Redis Vector
            async with RedisVectorService() as redis_vector:
                redis_result = await redis_vector.index_repository_code(analysis_id, repo_content)
                total_chunks = redis_result.get("chunks_created", 0)
                indexed_files = redis_result.get("files_indexed", 0)

                # Update progress incrementally for better UX
                for progress in range(10, 101, 10):
                    await self._update_indexing_status(analysis_id, "indexing", progress)
                    await asyncio.sleep(0.2)  # Small delay to make progress visible
            
            # Mark as completed with files and chunks count
            await self._update_indexing_status(analysis_id, "completed", 100, code_hash, indexed_files, total_chunks)
            
            result = {
                "status": "completed",
                "files_indexed": indexed_files,
                "total_chunks": total_chunks,
                "code_hash": code_hash,
                "indexed_at": datetime.utcnow().isoformat()
            }
            
            logger.info(f"Indexing completed for analysis {analysis_id}: {indexed_files} files, {total_chunks} chunks")
            return result
            
        except Exception as e:
            logger.error(f"Error indexing repository for analysis {analysis_id}: {str(e)}")
            await self._update_indexing_status(analysis_id, "failed", 0)
            raise Exception(f"Code indexing failed: {str(e)}")
    
    async def should_reindex(self, analysis_id: int, repo_content: Dict[str, Any]) -> bool:
        """
        Check if repository should be re-indexed based on code changes
        
        Args:
            analysis_id: ID of the analysis record
            repo_content: Current repository content
            
        Returns:
            True if re-indexing is needed
        """
        try:
            # Get current analysis record
            db = next(get_db())
            analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()
            
            if not analysis or not analysis.code_hash:
                return True  # No previous indexing, should index
            
            # Calculate current code hash
            current_hash = self._calculate_code_hash(repo_content)
            
            # Compare with stored hash
            return analysis.code_hash != current_hash
            
        except Exception as e:
            logger.error(f"Error checking reindex status: {str(e)}")
            return True  # Default to reindex on error
    
    async def get_indexing_status(self, analysis_id: int) -> Dict[str, Any]:
        """Get current indexing status for an analysis"""
        try:
            db = next(get_db())
            analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()
            
            if not analysis:
                return {"status": "not_found"}
            
            # Get files and chunks count safely
            files_indexed = getattr(analysis, 'files_indexed', 0) or 0
            total_chunks = getattr(analysis, 'total_chunks', 0) or 0

            return {
                "indexing_status": analysis.indexing_status or "never",
                "indexing_progress": analysis.indexing_progress or 0,
                "last_indexed_at": analysis.last_indexed_at.isoformat() if analysis.last_indexed_at else None,
                "vector_db_id": analysis.vector_db_id,
                "files_indexed": files_indexed,
                "total_chunks": total_chunks
            }
            
        except Exception as e:
            logger.error(f"Error getting indexing status: {str(e)}")
            return {"status": "error", "error": str(e)}
    
    def _calculate_code_hash(self, repo_content: Dict[str, Any]) -> str:
        """Calculate hash of repository code for change detection"""
        try:
            code_samples = repo_content.get("code_samples", {})
            
            # Create a deterministic string from all code content
            code_string = ""
            for file_path in sorted(code_samples.keys()):
                content = code_samples[file_path]
                if isinstance(content, str):
                    code_string += f"{file_path}:{content}\n"
            
            # Calculate SHA-256 hash
            return hashlib.sha256(code_string.encode('utf-8')).hexdigest()
            
        except Exception as e:
            logger.error(f"Error calculating code hash: {str(e)}")
            return "error"
    
    def _filter_indexable_files(self, code_samples: Dict[str, str]) -> Dict[str, str]:
        """Filter files that should be indexed with intelligent prioritization"""

        # File extensions to index (expanded)
        indexable_extensions = {
            '.py', '.js', '.ts', '.jsx', '.tsx', '.java', '.go', '.rs', '.cpp', '.c', '.h',
            '.php', '.rb', '.swift', '.kt', '.scala', '.clj', '.hs', '.ml', '.r', '.sql',
            '.yaml', '.yml', '.json', '.xml', '.toml', '.ini', '.cfg', '.conf', '.md',
            '.vue', '.svelte', '.dart', '.lua', '.perl', '.sh', '.bash', '.zsh'
        }

        # Files to skip
        skip_patterns = [
            'node_modules/', 'venv/', '.git/', '__pycache__/', '.pytest_cache/',
            'dist/', 'build/', 'target/', '.next/', '.nuxt/', 'coverage/',
            'package-lock.json', 'yarn.lock', 'poetry.lock', 'Pipfile.lock',
            '.min.js', '.min.css', 'bundle.js', 'vendor.js'
        ]

        # Priority files (always include if under size limit)
        priority_patterns = [
            'main.', 'index.', 'app.', 'server.', 'config.', 'settings.',
            'README', 'package.json', 'requirements.txt', 'Cargo.toml',
            'go.mod', 'pom.xml', 'build.gradle'
        ]

        # Categorize files by priority
        priority_files = {}
        regular_files = {}
        large_files = {}

        for file_path, content in code_samples.items():
            # Skip if not string content
            if not isinstance(content, str):
                continue

            # Skip files in excluded directories
            if any(pattern in file_path for pattern in skip_patterns):
                continue

            # Check file extension
            file_ext = '.' + file_path.split('.')[-1] if '.' in file_path else ''
            if file_ext.lower() not in indexable_extensions:
                continue

            file_name = file_path.split('/')[-1].lower()
            content_size = len(content)

            # Categorize by priority and size
            if any(pattern.lower() in file_name for pattern in priority_patterns):
                if content_size <= self.max_file_size:
                    priority_files[file_path] = content
                elif content_size <= self.max_file_size * 2:  # Allow larger priority files
                    large_files[file_path] = content
            elif content_size <= self.max_file_size:
                regular_files[file_path] = content
            elif content_size <= self.max_file_size * 2:
                large_files[file_path] = content

        # Build final selection with intelligent limits
        indexable = {}

        # Always include priority files
        indexable.update(priority_files)

        # Add regular files up to limit
        remaining_slots = max(0, self.max_total_files - len(indexable))
        for file_path, content in list(regular_files.items())[:remaining_slots]:
            indexable[file_path] = content

        # Add some large files if we have remaining slots
        remaining_slots = max(0, self.max_total_files - len(indexable))
        for file_path, content in list(large_files.items())[:min(remaining_slots, 10)]:
            # Truncate large files to manageable size
            indexable[file_path] = content[:self.max_file_size]

        logger.info(f"Selected {len(indexable)} files for indexing: "
                   f"{len(priority_files)} priority, "
                   f"{len([f for f in indexable if f in regular_files])} regular, "
                   f"{len([f for f in indexable if f in large_files])} large (truncated)")

        return indexable
    
    async def _index_file_content(self, analysis_id: int, file_path: str, content: str) -> List[Dict[str, Any]]:
        """Index content of a single file into overlapping chunks for better context"""
        chunks = []

        # Use overlapping chunks for better context preservation
        step_size = self.chunk_size - self.chunk_overlap

        for i in range(0, len(content), step_size):
            chunk_end = min(i + self.chunk_size, len(content))
            chunk_content = content[i:chunk_end]

            # Skip very small chunks at the end
            if len(chunk_content.strip()) < 50:
                break

            chunk = {
                "analysis_id": analysis_id,
                "file_path": file_path,
                "chunk_index": len(chunks),
                "content": chunk_content,
                "start_char": i,
                "end_char": chunk_end,
                "overlap_with_previous": i > 0,
                "file_size": len(content)
            }

            chunks.append(chunk)

        logger.debug(f"Indexed {len(chunks)} overlapping chunks from {file_path} "
                    f"(size: {len(content)} chars, overlap: {self.chunk_overlap})")

        return chunks
    
    async def _update_indexing_status(self, analysis_id: int, status: str,
                                    progress: int, code_hash: Optional[str] = None,
                                    files_indexed: Optional[int] = None,
                                    total_chunks: Optional[int] = None):
        """Update indexing status in database"""
        try:
            db = next(get_db())
            analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()

            if analysis:
                analysis.indexing_status = status
                analysis.indexing_progress = progress

                if status == "completed":
                    analysis.last_indexed_at = datetime.utcnow()
                    if code_hash:
                        analysis.code_hash = code_hash

                # Update files and chunks count if provided
                if files_indexed is not None:
                    # Try to set the attribute, handle gracefully if field doesn't exist yet
                    try:
                        analysis.files_indexed = files_indexed
                    except AttributeError:
                        logger.warning("files_indexed field not available in database model")

                if total_chunks is not None:
                    try:
                        analysis.total_chunks = total_chunks
                    except AttributeError:
                        logger.warning("total_chunks field not available in database model")

                db.commit()
                logger.debug(f"Updated indexing status for analysis {analysis_id}: {status} ({progress}%)")

        except Exception as e:
            logger.error(f"Error updating indexing status: {str(e)}")
    
    async def trigger_reindex(self, analysis_id: int, repo_content: Dict[str, Any]) -> Dict[str, Any]:
        """Trigger re-indexing of repository code"""
        logger.info(f"Triggering re-index for analysis {analysis_id}")
        
        # Reset indexing status
        await self._update_indexing_status(analysis_id, "not_started", 0)
        
        # Start indexing
        return await self.index_repository(analysis_id, repo_content)
