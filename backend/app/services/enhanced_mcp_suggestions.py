"""
Enhanced MCP Tool Suggestion Service
Generates specific, actionable MCP tools based on actual repository functionality
"""
import json
import logging
import re
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class MCPTool:
    """Represents a specific MCP tool suggestion"""
    name: str
    description: str
    implementation_type: str  # "function_wrapper", "api_proxy", "cli_wrapper", "data_processor"
    source_functions: List[str]
    source_files: List[str]
    input_schema: Dict[str, Any]
    business_value: str
    implementation_effort: str
    code_references: Dict[str, str]  # Maps tool functionality to actual code snippets


class EnhancedMCPSuggestionService:
    """Service for generating specific, actionable MCP tool suggestions"""
    
    def __init__(self):
        self.tool_extractors = {
            'api_endpoints': self._extract_api_tools,
            'functions': self._extract_function_tools,
            'cli_commands': self._extract_cli_tools,
            'data_processors': self._extract_data_tools,
            'integrations': self._extract_integration_tools
        }
    
    async def generate_specific_tools(self, analysis_data: Dict[str, Any]) -> List[MCPTool]:
        """Generate specific MCP tools based on actual repository functionality"""
        
        all_tools = []
        repo_context = self._build_repository_context(analysis_data)
        
        # Extract tools from different sources
        for extractor_name, extractor_func in self.tool_extractors.items():
            try:
                tools = await extractor_func(analysis_data, repo_context)
                all_tools.extend(tools)
                logger.info(f"Extracted {len(tools)} tools from {extractor_name}")
            except Exception as e:
                logger.error(f"Error extracting {extractor_name} tools: {str(e)}")
        
        # Deduplicate and prioritize
        unique_tools = self._deduplicate_tools(all_tools)
        prioritized_tools = self._prioritize_tools(unique_tools, repo_context)
        
        return prioritized_tools
    
    def _build_repository_context(self, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """Build context about the repository for tool generation"""
        
        repo_info = analysis_data.get('repository_info', {})
        business_logic = analysis_data.get('comprehensive_analysis', {}).get('business_logic', {})
        code_implementations = business_logic.get('code_implementations', {})
        
        return {
            'repo_name': repo_info.get('name', ''),
            'language': repo_info.get('language', '').lower(),
            'domain': business_logic.get('primary_domain', ''),
            'business_purpose': business_logic.get('business_purpose', ''),
            'functions': code_implementations.get('functions', []),
            'classes': code_implementations.get('classes', []),
            'api_endpoints': code_implementations.get('api_endpoints', []),
            'utilities': code_implementations.get('utilities', []),
            'api_info': analysis_data.get('comprehensive_analysis', {}).get('api_capabilities', {}),
            'workflows': analysis_data.get('comprehensive_analysis', {}).get('workflows', {})
        }
    
    async def _extract_api_tools(self, analysis_data: Dict[str, Any], context: Dict[str, Any]) -> List[MCPTool]:
        """Extract MCP tools from API endpoints"""
        tools = []
        
        api_endpoints = context.get('api_endpoints', [])
        existing_apis = context.get('api_info', {}).get('existing_apis', [])
        
        # Create tools for existing API endpoints
        for endpoint in api_endpoints:
            if not endpoint.get('name'):
                continue
                
            tool_name = f"{context['repo_name']}_{endpoint['name']}_api"
            
            # Build input schema based on endpoint signature
            input_schema = self._build_api_input_schema(endpoint)
            
            tool = MCPTool(
                name=tool_name,
                description=f"Execute {endpoint['name']} API endpoint from {context['repo_name']}",
                implementation_type="api_proxy",
                source_functions=[endpoint['name']],
                source_files=[endpoint.get('file', 'unknown')],
                input_schema=input_schema,
                business_value=f"Enables AI assistants to interact with {endpoint['name']} functionality",
                implementation_effort="low",
                code_references={
                    'endpoint_definition': endpoint.get('signature', ''),
                    'file_location': endpoint.get('file', ''),
                    'line_number': str(endpoint.get('line', 0))
                }
            )
            tools.append(tool)
        
        # Create tools for potential API consolidation
        if len(api_endpoints) > 3:
            consolidated_tool = MCPTool(
                name=f"{context['repo_name']}_api_gateway",
                description=f"Unified gateway for all {context['repo_name']} API endpoints",
                implementation_type="api_proxy",
                source_functions=[ep['name'] for ep in api_endpoints],
                source_files=list(set(ep.get('file', '') for ep in api_endpoints)),
                input_schema={
                    "type": "object",
                    "properties": {
                        "endpoint": {"type": "string", "enum": [ep['name'] for ep in api_endpoints]},
                        "parameters": {"type": "object"}
                    }
                },
                business_value="Single interface for all API functionality",
                implementation_effort="medium",
                code_references={'endpoint_count': str(len(api_endpoints))}
            )
            tools.append(consolidated_tool)
        
        return tools
    
    async def _extract_function_tools(self, analysis_data: Dict[str, Any], context: Dict[str, Any]) -> List[MCPTool]:
        """Extract MCP tools from business logic functions"""
        tools = []
        
        functions = context.get('functions', [])
        utilities = context.get('utilities', [])
        
        # Focus on non-utility functions that provide business value
        business_functions = [f for f in functions if not f.get('is_utility', False)]
        
        for func in business_functions[:10]:  # Limit to top 10 functions
            if not func.get('name'):
                continue
            
            # Skip private functions unless they're clearly business-critical
            if func['name'].startswith('_') and 'process' not in func['name'] and 'handle' not in func['name']:
                continue
            
            tool_name = f"{context['repo_name']}_{func['name']}"
            
            # Build input schema from function parameters
            input_schema = self._build_function_input_schema(func)
            
            tool = MCPTool(
                name=tool_name,
                description=f"Execute {func['name']} business logic from {context['repo_name']}",
                implementation_type="function_wrapper",
                source_functions=[func['name']],
                source_files=[func.get('file', 'unknown')],
                input_schema=input_schema,
                business_value=f"Provides access to {func['name']} functionality for AI workflows",
                implementation_effort="low" if len(func.get('parameters', '')) < 50 else "medium",
                code_references={
                    'function_signature': func.get('signature', ''),
                    'file_location': func.get('file', ''),
                    'body_preview': func.get('body_preview', '')[:200]
                }
            )
            tools.append(tool)
        
        return tools
    
    async def _extract_cli_tools(self, analysis_data: Dict[str, Any], context: Dict[str, Any]) -> List[MCPTool]:
        """Extract MCP tools from CLI commands"""
        tools = []
        
        # Look for CLI patterns in the codebase
        cli_indicators = []
        code_samples = analysis_data.get('code_samples', {})
        
        for file_path, content in code_samples.items():
            if any(cli_pattern in content.lower() for cli_pattern in [
                'argparse', 'click.', 'commander', 'yargs', 'cobra', 'cli.', 'command'
            ]):
                cli_indicators.append({
                    'file': file_path,
                    'content_preview': content[:500]
                })
        
        # If CLI patterns found, create CLI wrapper tools
        if cli_indicators:
            for cli_file in cli_indicators[:3]:  # Limit to 3 CLI files
                file_name = cli_file['file'].split('/')[-1].replace('.', '_')
                
                tool = MCPTool(
                    name=f"{context['repo_name']}_cli_{file_name}",
                    description=f"Execute CLI commands from {cli_file['file']}",
                    implementation_type="cli_wrapper",
                    source_functions=['cli_executor'],
                    source_files=[cli_file['file']],
                    input_schema={
                        "type": "object",
                        "properties": {
                            "command": {"type": "string", "description": "CLI command to execute"},
                            "arguments": {"type": "array", "items": {"type": "string"}}
                        }
                    },
                    business_value="Enables AI assistants to execute CLI operations",
                    implementation_effort="medium",
                    code_references={
                        'cli_file': cli_file['file'],
                        'content_preview': cli_file['content_preview']
                    }
                )
                tools.append(tool)
        
        return tools
    
    async def _extract_data_tools(self, analysis_data: Dict[str, Any], context: Dict[str, Any]) -> List[MCPTool]:
        """Extract MCP tools from data processing functionality"""
        tools = []
        
        # Look for data processing patterns
        data_indicators = self._find_data_processing_patterns(analysis_data)
        
        if data_indicators['has_database']:
            tool = MCPTool(
                name=f"{context['repo_name']}_database_query",
                description=f"Query database operations in {context['repo_name']}",
                implementation_type="data_processor",
                source_functions=data_indicators['db_functions'],
                source_files=data_indicators['db_files'],
                input_schema={
                    "type": "object",
                    "properties": {
                        "query_type": {"type": "string", "enum": ["select", "insert", "update", "delete"]},
                        "table": {"type": "string"},
                        "conditions": {"type": "object"}
                    }
                },
                business_value="Provides AI access to database operations",
                implementation_effort="medium",
                code_references=data_indicators['db_evidence']
            )
            tools.append(tool)
        
        if data_indicators['has_file_processing']:
            tool = MCPTool(
                name=f"{context['repo_name']}_file_processor",
                description=f"Process files using {context['repo_name']} functionality",
                implementation_type="data_processor",
                source_functions=data_indicators['file_functions'],
                source_files=data_indicators['file_files'],
                input_schema={
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string"},
                        "operation": {"type": "string", "enum": ["read", "process", "transform", "validate"]}
                    }
                },
                business_value="Enables AI file processing workflows",
                implementation_effort="low",
                code_references=data_indicators['file_evidence']
            )
            tools.append(tool)
        
        return tools

    async def _extract_integration_tools(self, analysis_data: Dict[str, Any], context: Dict[str, Any]) -> List[MCPTool]:
        """Extract MCP tools from integration points"""
        tools = []

        integration_opportunities = analysis_data.get('comprehensive_analysis', {}).get('integration_opportunities', {})
        external_services = integration_opportunities.get('external_services', [])

        for service in external_services[:5]:  # Limit to 5 integrations
            if not isinstance(service, dict):
                continue

            service_name = service.get('service', 'unknown_service')

            tool = MCPTool(
                name=f"{context['repo_name']}_{service_name}_integration",
                description=f"Integrate with {service_name} through {context['repo_name']}",
                implementation_type="integration_proxy",
                source_functions=[f"{service_name}_client"],
                source_files=[],
                input_schema={
                    "type": "object",
                    "properties": {
                        "action": {"type": "string"},
                        "parameters": {"type": "object"}
                    }
                },
                business_value=f"Provides AI access to {service_name} integration",
                implementation_effort="medium",
                code_references={
                    'service_purpose': service.get('purpose', ''),
                    'integration_method': service.get('integration_method', '')
                }
            )
            tools.append(tool)

        return tools

    def _build_api_input_schema(self, endpoint: Dict[str, Any]) -> Dict[str, Any]:
        """Build input schema for API endpoint tool"""
        schema = {
            "type": "object",
            "properties": {},
            "required": []
        }

        # Parse parameters from signature
        params = endpoint.get('parameters', '')
        if params:
            # Simple parameter parsing - could be enhanced
            param_names = [p.strip().split(':')[0].strip() for p in params.split(',') if p.strip()]
            for param in param_names:
                if param and param != 'self':
                    schema["properties"][param] = {"type": "string"}
                    schema["required"].append(param)

        return schema

    def _build_function_input_schema(self, func: Dict[str, Any]) -> Dict[str, Any]:
        """Build input schema for function tool"""
        schema = {
            "type": "object",
            "properties": {},
            "required": []
        }

        # Parse parameters from signature
        params = func.get('parameters', '')
        if params:
            param_names = [p.strip().split('=')[0].strip().split(':')[0].strip()
                          for p in params.split(',') if p.strip()]
            for param in param_names:
                if param and param != 'self':
                    schema["properties"][param] = {"type": "string"}
                    if '=' not in f"{param}=":  # Required if no default value
                        schema["required"].append(param)

        return schema

    def _find_data_processing_patterns(self, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """Find data processing patterns in the codebase"""
        patterns = {
            'has_database': False,
            'has_file_processing': False,
            'db_functions': [],
            'db_files': [],
            'db_evidence': {},
            'file_functions': [],
            'file_files': [],
            'file_evidence': {}
        }

        code_samples = analysis_data.get('code_samples', {})

        for file_path, content in code_samples.items():
            content_lower = content.lower()

            # Database patterns
            if any(db_pattern in content_lower for db_pattern in [
                'select ', 'insert ', 'update ', 'delete ', 'database', 'sql',
                'session.', 'query(', 'db.', 'cursor', 'execute('
            ]):
                patterns['has_database'] = True
                patterns['db_files'].append(file_path)
                patterns['db_evidence'][file_path] = content[:300]

            # File processing patterns
            if any(file_pattern in content_lower for file_pattern in [
                'open(', 'read(', 'write(', 'file.', 'csv', 'json.', 'xml',
                'process_file', 'file_handler', 'parse_'
            ]):
                patterns['has_file_processing'] = True
                patterns['file_files'].append(file_path)
                patterns['file_evidence'][file_path] = content[:300]

        return patterns

    def _deduplicate_tools(self, tools: List[MCPTool]) -> List[MCPTool]:
        """Remove duplicate tools based on name and functionality"""
        seen_names = set()
        unique_tools = []

        for tool in tools:
            if tool.name not in seen_names:
                seen_names.add(tool.name)
                unique_tools.append(tool)

        return unique_tools

    def _prioritize_tools(self, tools: List[MCPTool], context: Dict[str, Any]) -> List[MCPTool]:
        """Prioritize tools based on business value and implementation effort"""

        def priority_score(tool: MCPTool) -> float:
            score = 0.0

            # Implementation type priority
            type_scores = {
                'api_proxy': 0.9,  # High priority - direct API access
                'function_wrapper': 0.8,  # High priority - business logic
                'cli_wrapper': 0.6,  # Medium priority - CLI access
                'data_processor': 0.7,  # Medium-high priority - data access
                'integration_proxy': 0.5  # Medium priority - external integrations
            }
            score += type_scores.get(tool.implementation_type, 0.3)

            # Effort consideration (lower effort = higher priority)
            effort_scores = {'low': 0.3, 'medium': 0.2, 'high': 0.1}
            score += effort_scores.get(tool.implementation_effort, 0.1)

            # Business value indicators
            if 'api' in tool.name.lower():
                score += 0.2
            if len(tool.source_functions) > 1:
                score += 0.1
            if tool.code_references:
                score += 0.1

            return score

        return sorted(tools, key=priority_score, reverse=True)
