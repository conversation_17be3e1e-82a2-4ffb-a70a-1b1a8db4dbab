"""
Updated Analysis Workflow
Integration of enhanced MCP suggestion and functional generation services
"""

from .enhanced_mcp_suggestions import EnhancedMCPSuggestionService
from .functional_mcp_generator import FunctionalMCPGenerator
from .intelligent_analysis_service import IntelligentAnalysisService


class UpdatedAnalysisWorkflow:
    """Enhanced analysis workflow with functional MCP generation"""
    
    def __init__(self):
        self.intelligent_analysis = IntelligentAnalysisService()
        self.suggestion_service = EnhancedMCPSuggestionService()
        self.functional_generator = FunctionalMCPGenerator()
    
    async def analyze_repository_with_functional_mcps(
        self,
        repo_owner: str,
        repo_name: str,
        github_token: str,
        repo_info: dict = None
    ) -> dict:
        """
        Complete analysis workflow with functional MCP generation
        
        Returns comprehensive analysis + specific tools + functional server code
        """
        
        # Step 1: Comprehensive repository analysis (existing)
        analysis_results = await self.intelligent_analysis.analyze_repository_comprehensively(
            repo_owner, repo_name, github_token, repo_info
        )
        
        # Step 2: Generate specific, actionable MCP tools (NEW)
        specific_tools = await self.suggestion_service.generate_specific_tools(analysis_results)
        
        # Step 3: Generate functional MCP server code (NEW)
        functional_server = await self.functional_generator.generate_functional_server(
            analysis_results,
            specific_tools,
            language='python'  # Can be made configurable
        )
        
        # Step 4: Calculate enhanced metrics
        enhanced_metrics = self._calculate_enhanced_metrics(analysis_results, specific_tools)
        
        # Step 5: Generate implementation roadmap
        implementation_roadmap = self._generate_implementation_roadmap(specific_tools)
        
        return {
            # Existing analysis results
            **analysis_results,
            
            # Enhanced MCP suggestions (NEW)
            'specific_mcp_tools': [
                {
                    'name': tool.name,
                    'description': tool.description,
                    'implementation_type': tool.implementation_type,
                    'source_functions': tool.source_functions,
                    'source_files': tool.source_files,
                    'business_value': tool.business_value,
                    'implementation_effort': tool.implementation_effort,
                    'input_schema': tool.input_schema,
                    'code_references': tool.code_references
                }
                for tool in specific_tools
            ],
            
            # Functional server generation (NEW)
            'functional_mcp_server': functional_server,
            
            # Enhanced metrics (NEW)
            'enhanced_metrics': enhanced_metrics,
            
            # Implementation roadmap (NEW)
            'implementation_roadmap': implementation_roadmap,
            
            # Summary statistics
            'analysis_summary': {
                'total_specific_tools': len(specific_tools),
                'tool_breakdown': self._get_tool_breakdown(specific_tools),
                'estimated_implementation_time': self._estimate_total_implementation_time(specific_tools),
                'confidence_score': enhanced_metrics['confidence_score'],
                'implementation_complexity': enhanced_metrics['complexity_assessment']
            }
        }
    
    def _calculate_enhanced_metrics(self, analysis_results: dict, tools: list) -> dict:
        """Calculate enhanced metrics based on specific tools"""
        
        # Tool-based feasibility score
        tool_score = min(len(tools) * 10, 100)  # 10 points per tool, max 100
        
        # Implementation effort score
        effort_scores = {'low': 90, 'medium': 70, 'high': 50}
        avg_effort_score = sum(effort_scores.get(tool.implementation_effort, 50) for tool in tools) / len(tools) if tools else 0
        
        # Type diversity score
        type_diversity = len(set(tool.implementation_type for tool in tools))
        diversity_score = min(type_diversity * 20, 100)
        
        # Combined confidence score
        confidence_score = (tool_score * 0.4 + avg_effort_score * 0.3 + diversity_score * 0.3) / 100
        
        # Complexity assessment
        high_effort_tools = len([t for t in tools if t.implementation_effort == 'high'])
        medium_effort_tools = len([t for t in tools if t.implementation_effort == 'medium'])
        
        if high_effort_tools > 3:
            complexity = 'high'
        elif high_effort_tools > 0 or medium_effort_tools > 5:
            complexity = 'medium'
        else:
            complexity = 'low'
        
        return {
            'confidence_score': confidence_score,
            'tool_feasibility_score': tool_score,
            'avg_implementation_effort_score': avg_effort_score,
            'type_diversity_score': diversity_score,
            'complexity_assessment': complexity,
            'total_tools_count': len(tools),
            'high_effort_tools': high_effort_tools,
            'medium_effort_tools': medium_effort_tools,
            'low_effort_tools': len([t for t in tools if t.implementation_effort == 'low'])
        }
    
    def _generate_implementation_roadmap(self, tools: list) -> dict:
        """Generate phased implementation roadmap"""
        
        # Sort tools by implementation effort and business value
        def priority_score(tool):
            effort_scores = {'low': 3, 'medium': 2, 'high': 1}
            type_scores = {'api_proxy': 3, 'function_wrapper': 3, 'data_processor': 2, 'cli_wrapper': 2, 'integration_proxy': 1}
            
            return (
                effort_scores.get(tool.implementation_effort, 1) * 0.6 +
                type_scores.get(tool.implementation_type, 1) * 0.4
            )
        
        sorted_tools = sorted(tools, key=priority_score, reverse=True)
        
        # Phase 1: Quick wins (low effort, high value)
        phase_1 = [t for t in sorted_tools if t.implementation_effort == 'low'][:5]
        
        # Phase 2: Core features (medium effort)
        phase_2 = [t for t in sorted_tools if t.implementation_effort == 'medium'][:5]
        
        # Phase 3: Advanced features (high effort)
        phase_3 = [t for t in sorted_tools if t.implementation_effort == 'high'][:3]
        
        return {
            'phase_1_quick_wins': [
                {
                    'name': tool.name,
                    'description': tool.description,
                    'estimated_hours': 8,
                    'business_value': tool.business_value,
                    'dependencies': []
                }
                for tool in phase_1
            ],
            'phase_2_core_features': [
                {
                    'name': tool.name,
                    'description': tool.description,
                    'estimated_hours': 24,
                    'business_value': tool.business_value,
                    'dependencies': tool.source_files
                }
                for tool in phase_2
            ],
            'phase_3_advanced_features': [
                {
                    'name': tool.name,
                    'description': tool.description,
                    'estimated_hours': 48,
                    'business_value': tool.business_value,
                    'dependencies': tool.source_files
                }
                for tool in phase_3
            ],
            'total_estimated_hours': len(phase_1) * 8 + len(phase_2) * 24 + len(phase_3) * 48,
            'recommended_timeline': f"{len(phase_1) + len(phase_2) + len(phase_3)} tools over {((len(phase_1) * 8 + len(phase_2) * 24 + len(phase_3) * 48) / 40):.1f} weeks"
        }
    
    def _get_tool_breakdown(self, tools: list) -> dict:
        """Get breakdown of tools by type"""
        breakdown = {}
        
        for tool in tools:
            tool_type = tool.implementation_type
            if tool_type not in breakdown:
                breakdown[tool_type] = 0
            breakdown[tool_type] += 1
        
        return breakdown
    
    def _estimate_total_implementation_time(self, tools: list) -> dict:
        """Estimate total implementation time"""
        effort_hours = {'low': 8, 'medium': 24, 'high': 48}
        
        total_hours = sum(effort_hours.get(tool.implementation_effort, 24) for tool in tools)
        total_weeks = total_hours / 40  # Assuming 40-hour work week
        
        return {
            'total_hours': total_hours,
            'total_weeks': round(total_weeks, 1),
            'parallel_development_weeks': round(total_weeks / 2, 1) if len(tools) > 3 else total_weeks,
            'tools_per_week': round(len(tools) / max(total_weeks, 1), 1)
        }
