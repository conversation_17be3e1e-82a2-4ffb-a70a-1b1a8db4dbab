"""
Context7 MCP Client

Integrates with Context7 MCP documentation to discover available MCP servers.
Provides access to curated MCP server registry and documentation.
"""

import asyncio
import aiohttp
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import logging
from ..config import settings

logger = logging.getLogger(__name__)


@dataclass
class Context7MCPServer:
    """Represents an MCP server from Context7 registry"""
    name: str
    description: str
    category: str
    github_url: str
    npm_package: Optional[str]
    python_package: Optional[str]
    documentation_url: str
    author: str
    version: str
    tags: List[str]
    compatibility: List[str]
    installation_guide: str
    examples: List[str]


class Context7Client:
    """Client for Context7 MCP documentation and registry"""
    
    def __init__(self):
        self.base_url = settings.context7_mcp_url or "https://mcp.context7.com"
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def search_mcp_servers(self, integration_type: str, service_name: str) -> List[Context7MCPServer]:
        """
        Search Context7 registry for MCP servers matching integration type and service
        
        Args:
            integration_type: Type of integration (payment, email, sms, etc.)
            service_name: Name of the service (stripe, sendgrid, twilio, etc.)
            
        Returns:
            List of matching MCP servers from Context7 registry
        """
        if not self.base_url:
            logger.warning("Context7 MCP URL not configured, skipping Context7 discovery")
            return []
        
        logger.info(f"Searching Context7 for MCP servers: {integration_type} / {service_name}")
        
        try:
            # Search by category/integration type
            category_results = await self._search_by_category(integration_type)
            
            # Search by service name
            service_results = await self._search_by_service(service_name)
            
            # Search by tags
            tag_results = await self._search_by_tags([integration_type, service_name])
            
            # Combine and deduplicate results
            all_results = category_results + service_results + tag_results
            unique_results = self._deduplicate_servers(all_results)
            
            # Rank by relevance
            ranked_results = self._rank_servers(unique_results, integration_type, service_name)
            
            logger.info(f"Found {len(ranked_results)} MCP servers in Context7 registry")
            return ranked_results[:10]  # Return top 10 results
            
        except Exception as e:
            logger.error(f"Error searching Context7 registry: {str(e)}")
            return []
    
    async def get_server_details(self, server_name: str) -> Optional[Context7MCPServer]:
        """Get detailed information about a specific MCP server"""
        try:
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            async with self.session.get(f"{self.base_url}/api/servers/{server_name}") as response:
                if response.status == 200:
                    data = await response.json()
                    return self._parse_server_data(data)
                else:
                    logger.warning(f"Server {server_name} not found in Context7 registry")
                    return None
                    
        except Exception as e:
            logger.error(f"Error getting server details for {server_name}: {str(e)}")
            return None
    
    async def _search_by_category(self, category: str) -> List[Context7MCPServer]:
        """Search MCP servers by category"""
        try:
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            # Map integration types to Context7 categories
            category_mapping = {
                "payment": ["payment", "finance", "commerce"],
                "email": ["email", "communication", "messaging"],
                "sms": ["sms", "communication", "messaging"],
                "cloud_storage": ["storage", "cloud", "files"],
                "database": ["database", "data", "storage"],
                "analytics": ["analytics", "tracking", "metrics"]
            }
            
            categories = category_mapping.get(category, [category])
            results = []
            
            for cat in categories:
                async with self.session.get(f"{self.base_url}/api/servers/category/{cat}") as response:
                    if response.status == 200:
                        data = await response.json()
                        servers = [self._parse_server_data(server) for server in data.get("servers", [])]
                        results.extend([s for s in servers if s])
            
            return results
            
        except Exception as e:
            logger.error(f"Error searching by category {category}: {str(e)}")
            return []
    
    async def _search_by_service(self, service_name: str) -> List[Context7MCPServer]:
        """Search MCP servers by service name"""
        try:
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            async with self.session.get(
                f"{self.base_url}/api/servers/search",
                params={"q": service_name, "type": "service"}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    servers = [self._parse_server_data(server) for server in data.get("servers", [])]
                    return [s for s in servers if s]
                else:
                    return []
                    
        except Exception as e:
            logger.error(f"Error searching by service {service_name}: {str(e)}")
            return []
    
    async def _search_by_tags(self, tags: List[str]) -> List[Context7MCPServer]:
        """Search MCP servers by tags"""
        try:
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            results = []
            for tag in tags:
                async with self.session.get(
                    f"{self.base_url}/api/servers/search",
                    params={"q": tag, "type": "tag"}
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        servers = [self._parse_server_data(server) for server in data.get("servers", [])]
                        results.extend([s for s in servers if s])
            
            return results
            
        except Exception as e:
            logger.error(f"Error searching by tags {tags}: {str(e)}")
            return []
    
    def _parse_server_data(self, data: Dict[str, Any]) -> Optional[Context7MCPServer]:
        """Parse server data from Context7 API response"""
        try:
            return Context7MCPServer(
                name=data.get("name", ""),
                description=data.get("description", ""),
                category=data.get("category", ""),
                github_url=data.get("github_url", ""),
                npm_package=data.get("npm_package"),
                python_package=data.get("python_package"),
                documentation_url=data.get("documentation_url", ""),
                author=data.get("author", ""),
                version=data.get("version", "1.0.0"),
                tags=data.get("tags", []),
                compatibility=data.get("compatibility", []),
                installation_guide=data.get("installation_guide", ""),
                examples=data.get("examples", [])
            )
        except Exception as e:
            logger.error(f"Error parsing server data: {str(e)}")
            return None
    
    def _deduplicate_servers(self, servers: List[Context7MCPServer]) -> List[Context7MCPServer]:
        """Remove duplicate servers based on name and GitHub URL"""
        seen = set()
        unique_servers = []
        
        for server in servers:
            # Create a unique key based on name and GitHub URL
            key = f"{server.name}_{server.github_url}"
            if key not in seen:
                seen.add(key)
                unique_servers.append(server)
        
        return unique_servers
    
    def _rank_servers(self, servers: List[Context7MCPServer], 
                     integration_type: str, service_name: str) -> List[Context7MCPServer]:
        """Rank servers by relevance to the integration type and service"""
        def rank_key(server):
            score = 0.0
            
            # Exact service name match in name
            if service_name.lower() in server.name.lower():
                score += 1.0
            
            # Service name in description
            if service_name.lower() in server.description.lower():
                score += 0.5
            
            # Integration type match in category
            if integration_type.lower() in server.category.lower():
                score += 0.8
            
            # Integration type in tags
            if any(integration_type.lower() in tag.lower() for tag in server.tags):
                score += 0.6
            
            # Service name in tags
            if any(service_name.lower() in tag.lower() for tag in server.tags):
                score += 0.7
            
            # Boost for official/verified servers (if author is known)
            official_authors = ["anthropic", "context7", "mcp-official"]
            if any(author in server.author.lower() for author in official_authors):
                score += 0.3
            
            # Boost for servers with good documentation
            if server.documentation_url and server.installation_guide:
                score += 0.2
            
            # Boost for servers with examples
            if server.examples:
                score += 0.1
            
            return score
        
        return sorted(servers, key=rank_key, reverse=True)
    
    async def get_installation_instructions(self, server: Context7MCPServer) -> Dict[str, str]:
        """Get installation instructions for a specific MCP server"""
        instructions = {}
        
        # Python installation
        if server.python_package:
            instructions["python"] = f"""
# Install the MCP server
pip install {server.python_package}

# Add to your MCP configuration
{{
  "mcpServers": {{
    "{server.name}": {{
      "command": "python",
      "args": ["-m", "{server.python_package}"]
    }}
  }}
}}
"""
        
        # Node.js installation
        if server.npm_package:
            instructions["nodejs"] = f"""
# Install the MCP server
npm install {server.npm_package}

# Add to your MCP configuration
{{
  "mcpServers": {{
    "{server.name}": {{
      "command": "node",
      "args": ["{server.npm_package}"]
    }}
  }}
}}
"""
        
        # GitHub installation
        if server.github_url:
            instructions["github"] = f"""
# Clone the repository
git clone {server.github_url}
cd {server.name}

# Follow the installation guide in the repository
# {server.installation_guide if server.installation_guide else 'See README.md for setup instructions'}
"""
        
        return instructions
    
    async def get_migration_guide(self, server: Context7MCPServer, 
                                 from_service: str, integration_type: str) -> str:
        """Generate a migration guide from existing service to MCP server"""
        guide = f"""
# Migration Guide: {from_service} → {server.name}

## Overview
Replace your existing {from_service} integration with the {server.name} MCP server for a more lightweight and standardized approach.

## Benefits
- Reduced dependency footprint
- Standardized AI assistant interface
- Better error handling and logging
- Simplified authentication flow

## Migration Steps

### 1. Install MCP Server
{server.installation_guide if server.installation_guide else 'See installation instructions'}

### 2. Update Configuration
Remove {from_service} SDK dependencies and configure the MCP server instead.

### 3. Update Code
Replace direct {from_service} API calls with MCP tool calls through your AI assistant.

### 4. Test Integration
Verify that all {integration_type} functionality works through the MCP interface.

## Examples
{chr(10).join(server.examples) if server.examples else 'See documentation for usage examples'}

## Support
- Documentation: {server.documentation_url}
- GitHub: {server.github_url}
- Author: {server.author}
"""
        return guide
