"""
Functional MCP Server Generator
Generates working MCP servers based on actual repository analysis and extracted tools
"""
import json
from typing import Dict, List, Any, Optional
from datetime import datetime

from .enhanced_mcp_suggestions import MC<PERSON><PERSON>ool, EnhancedMCPSuggestionService


class FunctionalMCPGenerator:
    """Generates functional MCP server implementations"""
    
    def __init__(self):
        self.suggestion_service = EnhancedMCPSuggestionService()
    
    async def generate_functional_server(
        self, 
        repo_analysis: Dict[str, Any], 
        tools: List[MCPTool],
        language: str = 'python',
        server_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """Generate a functional MCP server based on specific tools"""
        
        if not server_name:
            repo_name = repo_analysis.get('repository_info', {}).get('name', 'unknown')
            server_name = f"{repo_name}-mcp-server"
        
        # Generate server configuration
        server_config = self._generate_server_metadata(repo_analysis, tools, server_name)
        
        # Generate functional implementations based on language
        if language == 'python':
            server_code = self._generate_python_functional_server(tools, server_config, repo_analysis)
        elif language in ['javascript', 'typescript']:
            server_code = self._generate_js_functional_server(tools, server_config, repo_analysis)
        else:
            raise ValueError(f"Language {language} not supported")
        
        # Generate deployment and documentation
        deployment_files = self._generate_deployment_files(server_name, language, tools)
        documentation = self._generate_functional_documentation(server_config, tools)
        
        return {
            'server_name': server_name,
            'language': language,
            'tools_count': len(tools),
            'server_config': server_config,
            'code_files': server_code,
            'deployment': deployment_files,
            'documentation': documentation,
            'generated_at': datetime.utcnow().isoformat()
        }
    
    def _generate_server_metadata(self, repo_analysis: Dict[str, Any], tools: List[MCPTool], server_name: str) -> Dict[str, Any]:
        """Generate server metadata and configuration"""
        
        repo_info = repo_analysis.get('repository_info', {})
        business_logic = repo_analysis.get('comprehensive_analysis', {}).get('business_logic', {})
        
        return {
            'name': server_name,
            'version': '1.0.0',
            'description': f"Functional MCP server for {repo_info.get('name', 'repository')} - {business_logic.get('business_purpose', 'Repository functionality')}",
            'repository_url': repo_info.get('html_url', ''),
            'primary_domain': business_logic.get('primary_domain', 'general'),
            'tools': [
                {
                    'name': tool.name,
                    'description': tool.description,
                    'implementation_type': tool.implementation_type,
                    'input_schema': tool.input_schema,
                    'source_functions': tool.source_functions,
                    'business_value': tool.business_value
                }
                for tool in tools
            ],
            'capabilities': {
                'api_tools': len([t for t in tools if t.implementation_type == 'api_proxy']),
                'function_tools': len([t for t in tools if t.implementation_type == 'function_wrapper']),
                'cli_tools': len([t for t in tools if t.implementation_type == 'cli_wrapper']),
                'data_tools': len([t for t in tools if t.implementation_type == 'data_processor'])
            }
        }
    
    def _generate_python_functional_server(self, tools: List[MCPTool], config: Dict[str, Any], repo_analysis: Dict[str, Any]) -> Dict[str, str]:
        """Generate functional Python MCP server"""
        
        # Main server file
        main_py = self._generate_python_main_server(tools, config, repo_analysis)
        
        # Tool implementations
        implementations_py = self._generate_python_implementations(tools, repo_analysis)
        
        # Requirements file
        requirements_txt = self._generate_python_requirements(tools, repo_analysis)
        
        # Configuration file
        config_py = self._generate_python_config(config, repo_analysis)
        
        return {
            'main.py': main_py,
            'implementations.py': implementations_py,
            'requirements.txt': requirements_txt,
            'config.py': config_py,
            'README.md': self._generate_readme(config, tools)
        }
    
    def _generate_python_main_server(self, tools: List[MCPTool], config: Dict[str, Any], repo_analysis: Dict[str, Any]) -> str:
        """Generate the main Python MCP server file"""
        
        # Import statements based on tool types
        imports = self._generate_python_imports(tools, repo_analysis)
        
        # Tool definitions
        tool_definitions = self._generate_python_tool_definitions(tools)
        
        # Tool handlers
        tool_handlers = self._generate_python_tool_handlers(tools, repo_analysis)
        
        return f'''#!/usr/bin/env python3
"""
{config['name']} - Functional MCP Server
{config['description']}

Generated from repository analysis with {len(tools)} functional tools.
"""

import asyncio
import json
import logging
import os
import sys
from typing import Any, Dict, List, Optional

# MCP imports
from mcp.server import Server
from mcp.server.stdio import stdio_server
from mcp import types

# Custom imports
{imports}

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("{config['name']}")

# Initialize MCP server
app = Server("{config['name']}")

# Import tool implementations
from implementations import *
from config import *

# Tool definitions
TOOLS = {tool_definitions}

@app.list_tools()
async def list_tools() -> List[types.Tool]:
    """List all available tools"""
    tools = []
    
    for tool_config in TOOLS:
        tools.append(types.Tool(
            name=tool_config["name"],
            description=tool_config["description"],
            inputSchema=tool_config["inputSchema"]
        ))
    
    logger.info(f"Listed {{len(tools)}} tools")
    return tools

@app.call_tool()
async def call_tool(name: str, arguments: Dict[str, Any]) -> List[types.TextContent]:
    """Execute a tool"""
    logger.info(f"Executing tool: {{name}} with arguments: {{arguments}}")
    
    try:
        # Find the tool configuration
        tool_config = next((t for t in TOOLS if t["name"] == name), None)
        if not tool_config:
            raise ValueError(f"Unknown tool: {{name}}")
        
        # Execute the tool based on its type
        result = await execute_tool(tool_config, arguments)
        
        return [types.TextContent(
            type="text",
            text=json.dumps(result, indent=2, default=str)
        )]
        
    except Exception as e:
        logger.error(f"Tool execution failed for {{name}}: {{str(e)}}")
        return [types.TextContent(
            type="text",
            text=json.dumps({{"error": str(e), "tool": name}}, indent=2)
        )]

async def execute_tool(tool_config: Dict[str, Any], arguments: Dict[str, Any]) -> Dict[str, Any]:
    """Route tool execution to appropriate handler"""
    
    tool_name = tool_config["name"]
    implementation_type = tool_config["implementation_type"]
    
    try:
        if implementation_type == "api_proxy":
            return await execute_api_tool(tool_name, arguments, tool_config)
        elif implementation_type == "function_wrapper":
            return await execute_function_tool(tool_name, arguments, tool_config)
        elif implementation_type == "cli_wrapper":
            return await execute_cli_tool(tool_name, arguments, tool_config)
        elif implementation_type == "data_processor":
            return await execute_data_tool(tool_name, arguments, tool_config)
        else:
            return await execute_generic_tool(tool_name, arguments, tool_config)
            
    except Exception as e:
        logger.error(f"Error executing {{tool_name}}: {{str(e)}}")
        return {{
            "status": "error",
            "error": str(e),
            "tool": tool_name,
            "arguments": arguments
        }}

{tool_handlers}

async def main():
    """Main entry point"""
    from config import SERVER_NAME, REPOSITORY_URL, PRIMARY_DOMAIN

    logger.info(f"Starting {{SERVER_NAME}} MCP Server")
    logger.info(f"Repository: {{REPOSITORY_URL or 'Unknown'}}")
    logger.info(f"Domain: {{PRIMARY_DOMAIN}}")
    logger.info(f"Available tools: {{len(TOOLS)}}")

    # Print tool summary
    for tool in TOOLS:
        logger.info(f"  - {{tool['name']}} ({{tool['implementation_type']}})")

    async with stdio_server() as (read_stream, write_stream):
        await app.run(
            read_stream,
            write_stream,
            app.create_initialization_options()
        )

if __name__ == "__main__":
    asyncio.run(main())
'''
    
    def _generate_python_imports(self, tools: List[MCPTool], repo_analysis: Dict[str, Any]) -> str:
        """Generate appropriate import statements based on tools"""
        imports = []
        
        # Check what imports we need based on tool types
        has_api_tools = any(t.implementation_type == 'api_proxy' for t in tools)
        has_cli_tools = any(t.implementation_type == 'cli_wrapper' for t in tools)
        has_data_tools = any(t.implementation_type == 'data_processor' for t in tools)
        
        if has_api_tools:
            imports.extend([
                'import aiohttp',
                'import requests',
                'from urllib.parse import urljoin'
            ])
        
        if has_cli_tools:
            imports.extend([
                'import subprocess',
                'import shlex'
            ])
        
        if has_data_tools:
            imports.extend([
                'import sqlite3',
                'import json',
                'import csv',
                'import pandas as pd'
            ])
        
        # Language-specific imports
        language = repo_analysis.get('repository_info', {}).get('language', '').lower()
        if language == 'python':
            imports.append('import importlib.util')
        
        return '\n'.join(imports)
    
    def _generate_python_tool_definitions(self, tools: List[MCPTool]) -> str:
        """Generate Python tool definitions"""
        tool_defs = []
        
        for tool in tools:
            tool_def = {
                'name': tool.name,
                'description': tool.description,
                'implementation_type': tool.implementation_type,
                'inputSchema': tool.input_schema,
                'source_functions': tool.source_functions,
                'source_files': tool.source_files,
                'code_references': tool.code_references
            }
            tool_defs.append(tool_def)
        
        return json.dumps(tool_defs, indent=4)

    def _generate_python_tool_handlers(self, tools: List[MCPTool], repo_analysis: Dict[str, Any]) -> str:
        """Generate specific tool handler implementations"""

        handlers = []

        # API tool handlers
        api_tools = [t for t in tools if t.implementation_type == 'api_proxy']
        if api_tools:
            handlers.append(self._generate_api_handler(api_tools, repo_analysis))

        # Function tool handlers
        function_tools = [t for t in tools if t.implementation_type == 'function_wrapper']
        if function_tools:
            handlers.append(self._generate_function_handler(function_tools, repo_analysis))

        # CLI tool handlers
        cli_tools = [t for t in tools if t.implementation_type == 'cli_wrapper']
        if cli_tools:
            handlers.append(self._generate_cli_handler(cli_tools, repo_analysis))

        # Data tool handlers
        data_tools = [t for t in tools if t.implementation_type == 'data_processor']
        if data_tools:
            handlers.append(self._generate_data_handler(data_tools, repo_analysis))

        return '\n\n'.join(handlers)

    def _generate_specific_function_implementation(self, tool: MCPTool, repo_analysis: Dict[str, Any]) -> str:
        """Generate specific implementation for a function tool"""

        tool_name = tool.name
        source_functions = tool.source_functions
        source_files = tool.source_files
        code_references = tool.code_references

        # Generate specific implementation based on the tool
        if 'strapi' in tool_name.lower() and 'run' in tool_name.lower():
            return self._generate_strapi_run_implementation(tool, repo_analysis)
        elif 'strapi' in tool_name.lower() and 'server' in tool_name.lower():
            return self._generate_strapi_server_implementation(tool, repo_analysis)
        else:
            return self._generate_generic_tool_implementation(tool, repo_analysis)

    def _generate_strapi_run_implementation(self, tool: MCPTool, repo_analysis: Dict[str, Any]) -> str:
        """Generate implementation for strapi_run tool"""

        return '''async def execute_strapi_run(arguments: Dict[str, Any], tool_config: Dict[str, Any]) -> Dict[str, Any]:
    """Execute Strapi run functionality"""

    try:
        # Extract arguments
        package_name = arguments.get('package', 'strapi')
        action = arguments.get('action', 'serve')

        # Build command based on the original function logic
        if action == 'serve':
            # Based on scripts/open-api/serve.js functionality
            command = ['node', 'scripts/open-api/serve.js', package_name]
        else:
            command = ['npm', 'run', action]

        # Execute the command in the repository directory
        import subprocess
        import os

        repo_path = os.getenv('REPO_PATH', '.')

        process = await asyncio.create_subprocess_exec(
            *command,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            cwd=repo_path
        )

        stdout, stderr = await process.communicate()

        return {
            "status": "success" if process.returncode == 0 else "error",
            "return_code": process.returncode,
            "stdout": stdout.decode('utf-8'),
            "stderr": stderr.decode('utf-8'),
            "command": ' '.join(command),
            "tool": "strapi_run",
            "package": package_name,
            "action": action
        }

    except Exception as e:
        return {
            "status": "error",
            "error": f"Strapi run execution failed: {str(e)}",
            "tool": "strapi_run"
        }'''

    def _generate_strapi_server_implementation(self, tool: MCPTool, repo_analysis: Dict[str, Any]) -> str:
        """Generate implementation for strapi_server tool"""

        return '''async def execute_strapi_server(arguments: Dict[str, Any], tool_config: Dict[str, Any]) -> Dict[str, Any]:
    """Execute Strapi server functionality"""

    try:
        # Extract arguments
        mode = arguments.get('mode', 'development')
        port = arguments.get('port', 1337)

        # Build Strapi server command
        if mode == 'development':
            command = ['npm', 'run', 'develop']
        elif mode == 'production':
            command = ['npm', 'run', 'start']
        else:
            command = ['npm', 'run', mode]

        # Set environment variables
        env = os.environ.copy()
        env['PORT'] = str(port)

        # Execute the command
        import subprocess
        import os

        repo_path = os.getenv('REPO_PATH', '.')

        process = await asyncio.create_subprocess_exec(
            *command,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            cwd=repo_path,
            env=env
        )

        # For server commands, we might want to start and return immediately
        # rather than waiting for completion

        return {
            "status": "started",
            "message": f"Strapi server started in {mode} mode on port {port}",
            "command": ' '.join(command),
            "tool": "strapi_server",
            "mode": mode,
            "port": port,
            "pid": process.pid
        }

    except Exception as e:
        return {
            "status": "error",
            "error": f"Strapi server execution failed: {str(e)}",
            "tool": "strapi_server"
        }'''

    def _generate_generic_tool_implementation(self, tool: MCPTool, repo_analysis: Dict[str, Any]) -> str:
        """Generate generic implementation for any tool"""

        tool_name = tool.name
        safe_name = tool_name.lower().replace('-', '_').replace(' ', '_')

        return f'''async def execute_{safe_name}(arguments: Dict[str, Any], tool_config: Dict[str, Any]) -> Dict[str, Any]:
    """Execute {tool_name} functionality"""

    try:
        source_functions = tool_config.get("source_functions", [])
        source_files = tool_config.get("source_files", [])
        code_references = tool_config.get("code_references", {{}})

        if not source_functions:
            return {{
                "status": "error",
                "error": "No source functions defined",
                "tool": "{tool_name}"
            }}

        primary_function = source_functions[0]

        # Try to execute based on file type
        if source_files:
            source_file = source_files[0]

            if source_file.endswith('.js') or source_file.endswith('.ts'):
                return await execute_nodejs_function(primary_function, arguments, source_file)
            elif source_file.endswith('.py'):
                return await execute_python_function(primary_function, arguments, source_file)
            elif source_file.endswith('.sh'):
                return await execute_shell_script(source_file, arguments)

        # Fallback to structured response
        return {{
            "status": "success",
            "message": f"Executed {{primary_function}} from {tool_name}",
            "function": primary_function,
            "arguments": arguments,
            "source_files": source_files,
            "tool": "{tool_name}",
            "execution_type": "simulated"
        }}

    except Exception as e:
        return {{
            "status": "error",
            "error": f"{tool_name} execution failed: {{str(e)}}",
            "tool": "{tool_name}"
        }}'''

    def _generate_api_handler(self, api_tools: List[MCPTool], repo_analysis: Dict[str, Any]) -> str:
        """Generate API proxy handler"""

        endpoint_mappings = []
        for tool in api_tools:
            endpoint_def = tool.code_references.get("endpoint_definition", "/api/unknown")
            endpoint_mappings.append(f'        "{tool.name}": "{endpoint_def}",')

        endpoint_mappings_str = '\n'.join(endpoint_mappings)

        return '''async def execute_api_tool(tool_name: str, arguments: Dict[str, Any], tool_config: Dict[str, Any]) -> Dict[str, Any]:
    """Execute API proxy tools"""

    # Get API configuration
    base_url = os.getenv('API_BASE_URL', 'http://localhost:8000')
    api_key = os.getenv('API_KEY', '')

    headers = {'Content-Type': 'application/json'}
    if api_key:
        headers['Authorization'] = f'Bearer {api_key}'

    # Map tool names to endpoints
    endpoint_mapping = {
        # Add specific mappings based on extracted API endpoints
''' + endpoint_mappings_str + '''
    }

    endpoint = endpoint_mapping.get(tool_name, '/api/unknown')
    url = urljoin(base_url, endpoint)

    try:
        async with aiohttp.ClientSession() as session:
            method = 'POST'

            async with session.request(method, url, json=arguments, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    return {
                        "status": "success",
                        "result": result,
                        "endpoint": endpoint,
                        "tool": tool_name
                    }
                else:
                    error_text = await response.text()
                    return {
                        "status": "error",
                        "error": f"API call failed: {response.status}",
                        "details": error_text,
                        "endpoint": endpoint
                    }

    except Exception as e:
        return {
            "status": "error",
            "error": f"Network error: {str(e)}",
            "endpoint": endpoint,
            "tool": tool_name
        }'''

    def _generate_function_handler(self, function_tools: List[MCPTool], repo_analysis: Dict[str, Any]) -> str:
        """Generate function wrapper handler with actual implementations"""

        # Generate specific implementations for each function tool
        tool_implementations = []

        for tool in function_tools:
            impl = self._generate_specific_function_implementation(tool, repo_analysis)
            tool_implementations.append(impl)

        tool_implementations_code = '\n\n'.join(tool_implementations)

        return f'''async def execute_function_tool(tool_name: str, arguments: Dict[str, Any], tool_config: Dict[str, Any]) -> Dict[str, Any]:
    """Execute function wrapper tools with actual implementations"""

    try:
        # Route to specific tool implementation
        if tool_name == "strapi_run":
            return await execute_strapi_run(arguments, tool_config)
        elif tool_name == "strapi_server":
            return await execute_strapi_server(arguments, tool_config)
        else:
            # Generic function execution for other tools
            return await execute_generic_function(tool_name, arguments, tool_config)

    except Exception as e:
        logger.error(f"Function tool execution failed for {{tool_name}}: {{str(e)}}")
        return {{
            "status": "error",
            "error": str(e),
            "tool": tool_name,
            "arguments": arguments
        }}

{tool_implementations_code}

async def execute_generic_function(tool_name: str, arguments: Dict[str, Any], tool_config: Dict[str, Any]) -> Dict[str, Any]:
    """Generic function execution for tools without specific implementations"""

    source_functions = tool_config.get("source_functions", [])
    source_files = tool_config.get("source_files", [])
    code_references = tool_config.get("code_references", {{}})

    if not source_functions:
        return {{
            "status": "error",
            "error": "No source functions defined for this tool",
            "tool": tool_name
        }}

    primary_function = source_functions[0]

    # Try to execute based on available information
    try:
        # For JavaScript/Node.js functions, use subprocess to execute
        if any(file.endswith('.js') for file in source_files):
            return await execute_nodejs_function(primary_function, arguments, source_files[0] if source_files else None)

        # For Python functions, try dynamic import
        elif any(file.endswith('.py') for file in source_files):
            return await execute_python_function(primary_function, arguments, source_files[0] if source_files else None)

        # For other languages, provide structured response with execution context
        else:
            return {{
                "status": "success",
                "message": f"Function {{primary_function}} would be executed",
                "function_name": primary_function,
                "arguments": arguments,
                "source_files": source_files,
                "execution_context": {{
                    "function_signature": code_references.get("function_signature", ""),
                    "file_location": code_references.get("file_location", ""),
                    "body_preview": code_references.get("body_preview", "")[:200]
                }},
                "tool": tool_name,
                "note": "Function execution simulated - actual implementation depends on runtime environment"
            }}

    except Exception as e:
        return {{
            "status": "error",
            "error": f"Generic function execution failed: {{str(e)}}",
            "tool": tool_name
        }}'''

    def _generate_cli_handler(self, cli_tools: List[MCPTool], repo_analysis: Dict[str, Any]) -> str:
        """Generate CLI wrapper handler"""

        return '''async def execute_cli_tool(tool_name: str, arguments: Dict[str, Any], tool_config: Dict[str, Any]) -> Dict[str, Any]:
    """Execute CLI wrapper tools"""

    try:
        command = arguments.get('command', '')
        args = arguments.get('arguments', [])

        if not command:
            return {
                "status": "error",
                "error": "No command specified",
                "tool": tool_name
            }

        # Build the full command
        full_command = [command] + args

        # Execute the command
        process = await asyncio.create_subprocess_exec(
            *full_command,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            cwd=os.getenv('REPO_PATH', '.')  # Set to repository path
        )

        stdout, stderr = await process.communicate()

        return {
            "status": "success" if process.returncode == 0 else "error",
            "return_code": process.returncode,
            "stdout": stdout.decode('utf-8'),
            "stderr": stderr.decode('utf-8'),
            "command": ' '.join(full_command),
            "tool": tool_name
        }

    except Exception as e:
        return {
            "status": "error",
            "error": f"CLI execution failed: {str(e)}",
            "tool": tool_name
        }'''

    def _generate_data_handler(self, data_tools: List[MCPTool], repo_analysis: Dict[str, Any]) -> str:
        """Generate data processor handler"""

        return '''async def execute_data_tool(tool_name: str, arguments: Dict[str, Any], tool_config: Dict[str, Any]) -> Dict[str, Any]:
    """Execute data processing tools"""

    try:
        operation = arguments.get('operation', 'read')

        if 'database' in tool_name:
            return await execute_database_operation(arguments, tool_config)
        elif 'file' in tool_name:
            return await execute_file_operation(arguments, tool_config)
        else:
            return await execute_generic_data_operation(arguments, tool_config)

    except Exception as e:
        return {
            "status": "error",
            "error": f"Data processing failed: {str(e)}",
            "tool": tool_name
        }

async def execute_database_operation(arguments: Dict[str, Any], tool_config: Dict[str, Any]) -> Dict[str, Any]:
    """Execute database operations"""
    # This is a placeholder - implement based on your actual database setup
    query_type = arguments.get('query_type', 'select')
    table = arguments.get('table', '')

    return {
        "status": "success",
        "message": f"Would execute {query_type} on table: {table}",
        "arguments": arguments,
        "note": "Implement actual database connection and query execution"
    }

async def execute_file_operation(arguments: Dict[str, Any], tool_config: Dict[str, Any]) -> Dict[str, Any]:
    """Execute file operations"""
    file_path = arguments.get('file_path', '')
    operation = arguments.get('operation', 'read')

    if not file_path:
        return {"status": "error", "error": "No file path specified"}

    try:
        if operation == 'read':
            with open(file_path, 'r') as f:
                content = f.read()
            return {
                "status": "success",
                "content": content[:1000],  # Limit content size
                "file_path": file_path,
                "operation": operation
            }
        else:
            return {
                "status": "success",
                "message": f"Would execute {operation} on file: {file_path}",
                "note": "Implement specific file operations based on your needs"
            }

    except Exception as e:
        return {
            "status": "error",
            "error": f"File operation failed: {str(e)}",
            "file_path": file_path
        }

async def execute_generic_data_operation(arguments: Dict[str, Any], tool_config: Dict[str, Any]) -> Dict[str, Any]:
    """Execute generic data operations"""
    return {
        "status": "success",
        "message": "Generic data operation executed",
        "arguments": arguments,
        "tool_config": tool_config
    }'''

    def _generate_python_implementations(self, tools: List[MCPTool], repo_analysis: Dict[str, Any]) -> str:
        """Generate implementations.py file with helper functions"""

        return '''"""
Tool Implementation Helpers
Contains helper functions and utilities for MCP tool execution
"""

import os
import json
import logging
from typing import Any, Dict, List, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

# Configuration constants
API_BASE_URL = os.getenv('API_BASE_URL', 'http://localhost:8000')
REPO_PATH = os.getenv('REPO_PATH', '.')
DB_PATH = os.getenv('DB_PATH', 'database.db')

class ToolExecutionError(Exception):
    """Custom exception for tool execution errors"""
    pass

def validate_arguments(arguments: Dict[str, Any], required_fields: List[str]) -> bool:
    """Validate that required arguments are present"""
    missing_fields = [field for field in required_fields if field not in arguments]
    if missing_fields:
        raise ToolExecutionError(f"Missing required fields: {missing_fields}")
    return True

def format_tool_response(status: str, data: Any = None, error: str = None, metadata: Dict[str, Any] = None) -> Dict[str, Any]:
    """Format standardized tool response"""
    response = {
        "status": status,
        "timestamp": str(datetime.utcnow()),
    }

    if data is not None:
        response["data"] = data
    if error:
        response["error"] = error
    if metadata:
        response["metadata"] = metadata

    return response

async def execute_generic_tool(tool_name: str, arguments: Dict[str, Any], tool_config: Dict[str, Any]) -> Dict[str, Any]:
    """Generic tool execution fallback"""
    logger.info(f"Executing generic tool: {tool_name}")

    return format_tool_response(
        status="success",
        data={
            "message": f"Generic execution of {tool_name}",
            "arguments": arguments,
            "source_functions": tool_config.get("source_functions", []),
            "implementation_type": tool_config.get("implementation_type", "unknown")
        },
        metadata={
            "tool_name": tool_name,
            "execution_type": "generic"
        }
    )

async def execute_nodejs_function(function_name: str, arguments: Dict[str, Any], source_file: str) -> Dict[str, Any]:
    """Execute a Node.js function from a source file"""
    try:
        repo_path = os.getenv('REPO_PATH', '.')
        file_path = os.path.join(repo_path, source_file)

        if not os.path.exists(file_path):
            return {
                "status": "error",
                "error": f"Source file not found: {source_file}",
                "function": function_name
            }

        # For Node.js files, execute them directly with node
        import subprocess
        import asyncio

        # Create command to execute the function
        js_code = f"""
const module = require('./{source_file}');
const func = module.{function_name} || module.default || module;
if (typeof func === 'function') {{
    const args = {json.dumps(arguments)};
    const result = func(args);
    console.log(JSON.stringify({{ status: "success", result: result }}));
}} else {{
    console.log(JSON.stringify({{ status: "error", error: "Function not found" }}));
}}
"""
        command = ['node', '-e', js_code]

        process = await asyncio.create_subprocess_exec(
            *command,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            cwd=repo_path
        )

        stdout, stderr = await process.communicate()

        if process.returncode == 0:
            try:
                result = json.loads(stdout.decode('utf-8'))
                return result
            except json.JSONDecodeError:
                return {
                    "status": "success",
                    "result": stdout.decode('utf-8'),
                    "function": function_name
                }
        else:
            return {
                "status": "error",
                "error": stderr.decode('utf-8'),
                "function": function_name
            }

    except Exception as e:
        return {
            "status": "error",
            "error": f"Node.js execution failed: {str(e)}",
            "function": function_name
        }

async def execute_python_function(function_name: str, arguments: Dict[str, Any], source_file: str) -> Dict[str, Any]:
    """Execute a Python function from a source file"""
    try:
        repo_path = os.getenv('REPO_PATH', '.')
        file_path = os.path.join(repo_path, source_file)

        if not os.path.exists(file_path):
            return {
                "status": "error",
                "error": f"Source file not found: {source_file}",
                "function": function_name
            }

        # Dynamic import and execution
        import importlib.util
        import sys

        spec = importlib.util.spec_from_file_location("dynamic_module", file_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)

        if hasattr(module, function_name):
            func = getattr(module, function_name)
            if callable(func):
                result = func(**arguments)
                return {
                    "status": "success",
                    "result": result,
                    "function": function_name
                }
            else:
                return {
                    "status": "error",
                    "error": f"'{function_name}' is not callable",
                    "function": function_name
                }
        else:
            return {
                "status": "error",
                "error": f"Function '{function_name}' not found in {source_file}",
                "function": function_name
            }

    except Exception as e:
        return {
            "status": "error",
            "error": f"Python execution failed: {str(e)}",
            "function": function_name
        }

async def execute_shell_script(script_file: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
    """Execute a shell script"""
    try:
        repo_path = os.getenv('REPO_PATH', '.')
        file_path = os.path.join(repo_path, script_file)

        if not os.path.exists(file_path):
            return {
                "status": "error",
                "error": f"Script file not found: {script_file}"
            }

        # Build command with arguments
        cmd = ['bash', file_path]
        for key, value in arguments.items():
            cmd.extend([f"--{key}", str(value)])

        import subprocess
        import asyncio

        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            cwd=repo_path
        )

        stdout, stderr = await process.communicate()

        return {
            "status": "success" if process.returncode == 0 else "error",
            "return_code": process.returncode,
            "stdout": stdout.decode('utf-8'),
            "stderr": stderr.decode('utf-8'),
            "script": script_file
        }

    except Exception as e:
        return {
            "status": "error",
            "error": f"Shell script execution failed: {str(e)}",
            "script": script_file
        }
'''

    def _generate_python_requirements(self, tools: List[MCPTool], repo_analysis: Dict[str, Any]) -> str:
        """Generate requirements.txt based on tool needs"""

        base_requirements = [
            "mcp>=1.0.0",
            "asyncio",
            "aiohttp>=3.8.0",
            "requests>=2.28.0"
        ]

        # Add requirements based on tool types
        has_data_tools = any(t.implementation_type == 'data_processor' for t in tools)
        has_api_tools = any(t.implementation_type == 'api_proxy' for t in tools)

        if has_data_tools:
            base_requirements.extend([
                "pandas>=1.5.0",
                "sqlite3",
                "sqlalchemy>=1.4.0"
            ])

        if has_api_tools:
            base_requirements.extend([
                "httpx>=0.24.0",
                "pydantic>=1.10.0"
            ])

        # Language-specific requirements
        language = repo_analysis.get('repository_info', {}).get('language', '').lower()
        if language == 'python':
            base_requirements.append("importlib-metadata>=4.0.0")

        return '\n'.join(sorted(set(base_requirements)))

    def _generate_python_config(self, config: Dict[str, Any], repo_analysis: Dict[str, Any]) -> str:
        """Generate config.py file"""

        return f'''"""
Configuration for {config['name']}
"""

import os
from typing import Dict, Any

# Server configuration
SERVER_NAME = "{config['name']}"
SERVER_VERSION = "{config['version']}"
SERVER_DESCRIPTION = """{config['description']}"""

# Repository information
REPOSITORY_URL = "{config.get('repository_url', '')}"
PRIMARY_DOMAIN = "{config.get('primary_domain', 'general')}"

# Tool configuration
TOOL_CAPABILITIES = {json.dumps(config.get('capabilities', {}), indent=4)}

# Environment configuration
API_BASE_URL = os.getenv('API_BASE_URL', 'http://localhost:8000')
API_KEY = os.getenv('API_KEY', '')
REPO_PATH = os.getenv('REPO_PATH', '.')
DB_PATH = os.getenv('DB_PATH', 'database.db')
LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')

# Tool-specific configuration
ENABLE_API_TOOLS = os.getenv('ENABLE_API_TOOLS', 'true').lower() == 'true'
ENABLE_CLI_TOOLS = os.getenv('ENABLE_CLI_TOOLS', 'true').lower() == 'true'
ENABLE_DATA_TOOLS = os.getenv('ENABLE_DATA_TOOLS', 'true').lower() == 'true'

def get_tool_config(tool_name: str) -> Dict[str, Any]:
    """Get configuration for a specific tool"""
    # Implement tool-specific configuration logic
    return {{
        "enabled": True,
        "timeout": 30,
        "retry_count": 3
    }}
'''

    def _generate_readme(self, config: Dict[str, Any], tools: List[MCPTool]) -> str:
        """Generate README.md for the MCP server"""

        tool_list = '\n'.join([f"- **{tool.name}**: {tool.description} (`{tool.implementation_type}`)"
                              for tool in tools])

        tools_section = '\n'.join([
            f"#### {tool.name}\n{tool.description}\n\n"
            f"**Implementation Type**: {tool.implementation_type}\n"
            f"**Source Functions**: {', '.join(tool.source_functions)}\n"
            f"**Business Value**: {tool.business_value}\n\n"
            for tool in tools
        ])

        readme_content = f"""# {config['name']}

{config['description']}

## Features

This MCP server provides {len(tools)} functional tools based on the analyzed repository:

{tool_list}

## Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Configure environment variables:
```bash
export API_BASE_URL="http://your-api-url"
export REPO_PATH="/path/to/repository"
# Add other configuration as needed
```

3. Run the server:
```bash
python main.py
```

## Configuration

Configure in your MCP client settings:

```json
{{
  "mcpServers": {{
    "{config['name']}": {{
      "command": "python",
      "args": ["main.py"],
      "cwd": "/path/to/this/server"
    }}
  }}
}}
```

## Tools

### Available Tools

{tools_section}

## Development

This server was generated from repository analysis. To customize:

1. Modify tool implementations in `implementations.py`
2. Update configuration in `config.py`
3. Add new tools by extending the TOOLS array in `main.py`

## Repository Information

- **Repository**: {config.get('repository_url', 'Unknown')}
- **Domain**: {config.get('primary_domain', 'general')}
- **Generated**: {datetime.utcnow().isoformat()}

"""
        return readme_content

    def _generate_js_functional_server(self, tools: List[MCPTool], config: Dict[str, Any], repo_analysis: Dict[str, Any]) -> Dict[str, str]:
        """Generate functional JavaScript/TypeScript MCP server"""
        # Similar implementation for JS/TS
        # This is a placeholder - implement based on JavaScript MCP patterns

        return {
            'main.js': f'// JavaScript MCP Server for {config["name"]}\n// {len(tools)} tools implemented\nconsole.log("Generated functional MCP server");',
            'package.json': json.dumps({
                "name": config['name'],
                "version": config['version'],
                "description": config['description'],
                "main": "main.js",
                "dependencies": {
                    "@modelcontextprotocol/sdk": "^1.0.0"
                }
            }, indent=2)
        }

    def _generate_deployment_files(self, server_name: str, language: str, tools: List[MCPTool]) -> Dict[str, str]:
        """Generate deployment configuration files"""

        dockerfile = f'''FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    curl \\
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Set environment variables
ENV PYTHONPATH=/app
ENV LOG_LEVEL=INFO

# Expose port (if needed)
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \\
    CMD python -c "import sys; sys.exit(0)"

# Run the server
CMD ["python", "main.py"]
'''

        docker_compose = f'''version: '3.8'

services:
  {server_name}:
    build: .
    container_name: {server_name}
    environment:
      - LOG_LEVEL=INFO
      - API_BASE_URL=http://localhost:8000
      - REPO_PATH=/app/repo
    volumes:
      - ./repo:/app/repo:ro
    restart: unless-stopped
    # Uncomment if you need port mapping
    # ports:
    #   - "8000:8000"

# Uncomment if you need additional services like databases
# networks:
#   mcp-network:
#     driver: bridge
'''

        return {
            'Dockerfile': dockerfile,
            'docker-compose.yml': docker_compose,
            '.env.example': f'''# Configuration for {server_name}
API_BASE_URL=http://localhost:8000
API_KEY=your_api_key_here
REPO_PATH=/path/to/repository
DB_PATH=database.db
LOG_LEVEL=INFO

# Tool-specific configuration
ENABLE_API_TOOLS=true
ENABLE_CLI_TOOLS=true
ENABLE_DATA_TOOLS=true
'''
        }

    def _generate_functional_documentation(self, config: Dict[str, Any], tools: List[MCPTool]) -> Dict[str, str]:
        """Generate comprehensive documentation"""

        api_docs = self._generate_api_documentation(tools)
        deployment_guide = self._generate_deployment_guide(config, tools)

        return {
            'API.md': api_docs,
            'DEPLOYMENT.md': deployment_guide,
            'CHANGELOG.md': f'# Changelog\n\n## v{config["version"]} - {datetime.utcnow().date()}\n\n- Initial release with {len(tools)} functional tools\n- Generated from repository analysis\n'
        }

    def _generate_api_documentation(self, tools: List[MCPTool]) -> str:
        """Generate API documentation"""

        tool_docs = []

        for tool in tools:
            code_refs = '\n'.join([f"- **{k}**: {v}" for k, v in tool.code_references.items()])

            doc = f"""## {tool.name}

**Description**: {tool.description}
**Implementation Type**: {tool.implementation_type}
**Business Value**: {tool.business_value}

### Input Schema
```json
{json.dumps(tool.input_schema, indent=2)}
```

### Source Information
- **Functions**: {', '.join(tool.source_functions)}
- **Files**: {', '.join(tool.source_files)}

### Code References
{code_refs}

---
"""
            tool_docs.append(doc)

        all_tool_docs = ''.join(tool_docs)

        return f"""# API Documentation

This document describes the {len(tools)} tools available in this MCP server.

{all_tool_docs}

## Error Handling

All tools return responses in the following format:

```json
{{
  "status": "success|error",
  "data": "...",
  "error": "error message if applicable",
  "metadata": {{
    "tool_name": "...",
    "timestamp": "..."
  }}
}}
```
"""

    def _generate_deployment_guide(self, config: Dict[str, Any], tools: List[MCPTool]) -> str:
        """Generate deployment guide"""

        tool_setup_sections = []
        for tool in tools:
            source_files_str = ', '.join(tool.source_files)
            tool_setup_sections.append(
                f"### {tool.name}\n"
                f"- Implementation Type: {tool.implementation_type}\n"
                f"- Setup Requirements: Configure based on source files: {source_files_str}\n\n"
            )

        tool_setup_content = ''.join(tool_setup_sections)

        return f"""# Deployment Guide for {config['name']}

## Prerequisites

- Python 3.11+
- Access to the original repository (if function tools are used)
- API credentials (if API tools are used)

## Configuration

1. Copy `.env.example` to `.env`
2. Update configuration values:
   - `API_BASE_URL`: Base URL for API tools
   - `REPO_PATH`: Path to the analyzed repository
   - `API_KEY`: Authentication credentials

## Local Development

```bash
# Install dependencies
pip install -r requirements.txt

# Set environment variables
export API_BASE_URL="http://localhost:8000"
export REPO_PATH="/path/to/repository"

# Run the server
python main.py
```

## Docker Deployment

```bash
# Build the image
docker build -t {config['name']} .

# Run with docker-compose
docker-compose up -d
```

## Tool-Specific Setup

{tool_setup_content}

## Health Checks

The server includes basic health checks. Monitor logs for:
- Tool execution status
- API connectivity (for API tools)
- Repository access (for function tools)

## Troubleshooting

Common issues:
1. **Tool execution failures**: Check repository path and permissions
2. **API connection errors**: Verify API_BASE_URL and credentials
3. **Import errors**: Ensure all dependencies are installed

## Production Considerations

- Use environment variables for sensitive configuration
- Set up proper logging and monitoring
- Consider rate limiting for API tools
- Implement proper error handling for production workloads
"""
