from sqlalchemy import Column, Integer, String, DateTime, <PERSON>olean, Text, JSON, ForeignKey, Float
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from ..database import Base


class RepoAnalysis(Base):
    __tablename__ = "repo_analyses"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    repo_url = Column(String, nullable=False)
    repo_name = Column(String, nullable=False)
    repo_owner = Column(String, nullable=False)
    status = Column(String, default="pending")  # pending, analyzing, completed, failed
    mcp_feasibility_score = Column(Float, nullable=True)
    analysis_results = Column(JSON, nullable=True)
    error_message = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)

    # New fields for integration detection and indexing
    indexing_status = Column(String, default="not_started")  # not_started, indexing, completed, failed
    indexing_progress = Column(Integer, default=0)  # 0-100
    vector_db_id = Column(String, nullable=True)  # Vector DB document ID
    integration_analysis = Column(JSON, nullable=True)  # Detected integrations
    last_indexed_at = Column(DateTime(timezone=True), nullable=True)
    code_hash = Column(String, nullable=True)  # To detect changes for re-indexing
    files_indexed = Column(Integer, default=0)  # Number of files indexed
    total_chunks = Column(Integer, default=0)  # Total number of code chunks created

    # Enhanced MCP analysis fields
    mcp_suggestions = Column(JSON, nullable=True)  # Enhanced MCP tool suggestions
    specific_tools = Column(JSON, nullable=True)  # Specific extracted tools
    functional_server_config = Column(JSON, nullable=True)  # Generated server configuration
    implementation_roadmap = Column(JSON, nullable=True)  # Phased implementation plan
    enhanced_metrics = Column(JSON, nullable=True)  # Enhanced analysis metrics
    tools_confidence_score = Column(Float, nullable=True)  # Confidence in tool extraction

    # Relationships
    user = relationship("User", back_populates="analyses")
    dependencies = relationship("Dependency", back_populates="analysis", cascade="all, delete-orphan")
    detected_integrations = relationship("DetectedIntegration", back_populates="analysis", cascade="all, delete-orphan")
    extracted_tools = relationship("ExtractedMCPTool", back_populates="analysis", cascade="all, delete-orphan")


class Dependency(Base):
    __tablename__ = "dependencies"

    id = Column(Integer, primary_key=True, index=True)
    analysis_id = Column(Integer, ForeignKey("repo_analyses.id"), nullable=False)
    name = Column(String, nullable=False)
    version = Column(String, nullable=True)
    language = Column(String, nullable=False)  # python, javascript, go, java, etc.
    dependency_type = Column(String, nullable=False)  # direct, dev, peer, etc.
    file_path = Column(String, nullable=False)  # requirements.txt, package.json, etc.
    mcp_potential = Column(Float, default=0.0)  # 0-1 score for MCP server potential
    existing_mcp_servers = Column(JSON, nullable=True)  # List of recommended MCP servers
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    analysis = relationship("RepoAnalysis", back_populates="dependencies")


class DetectedIntegration(Base):
    __tablename__ = "detected_integrations"

    id = Column(Integer, primary_key=True, index=True)
    analysis_id = Column(Integer, ForeignKey("repo_analyses.id"), nullable=False)
    integration_type = Column(String, nullable=False)  # payment, email, sms, etc.
    service_name = Column(String, nullable=False)  # stripe, sendgrid, twilio
    detection_method = Column(String, nullable=False)  # package, code_pattern, env_var
    confidence = Column(Float, nullable=False, default=0.0)  # 0.0 to 1.0
    file_locations = Column(JSON, nullable=True)  # Where it was found
    package_names = Column(JSON, nullable=True)  # Related packages
    code_patterns = Column(JSON, nullable=True)  # Detected code patterns
    env_variables = Column(JSON, nullable=True)  # Environment variables
    migration_complexity = Column(String, nullable=False)  # low, medium, high
    description = Column(Text, nullable=True)
    mcp_alternatives = Column(JSON, nullable=True)  # Available MCP alternatives
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    analysis = relationship("RepoAnalysis", back_populates="detected_integrations")


class ExtractedMCPTool(Base):
    __tablename__ = "extracted_mcp_tools"

    id = Column(Integer, primary_key=True, index=True)
    analysis_id = Column(Integer, ForeignKey("repo_analyses.id"), nullable=False)
    name = Column(String, nullable=False)  # Tool name
    description = Column(Text, nullable=False)  # Tool description
    implementation_type = Column(String, nullable=False)  # api_proxy, function_wrapper, etc.
    source_functions = Column(JSON, nullable=True)  # List of source functions
    source_files = Column(JSON, nullable=True)  # List of source files
    business_value = Column(Text, nullable=True)  # Business value description
    implementation_effort = Column(String, nullable=False)  # low, medium, high
    input_schema = Column(JSON, nullable=True)  # JSON schema for tool inputs
    code_references = Column(JSON, nullable=True)  # Code snippets and references
    priority_score = Column(Float, nullable=True)  # Priority ranking
    estimated_hours = Column(Integer, nullable=True)  # Implementation time estimate
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    analysis = relationship("RepoAnalysis", back_populates="extracted_tools")


class MCPServer(Base):
    __tablename__ = "mcp_servers"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False, index=True)
    url = Column(String, nullable=False, unique=True)
    description = Column(Text, nullable=True)
    category = Column(String, nullable=False, index=True)  # database, ai_chat, development, etc.
    github_url = Column(String, nullable=True)
    npm_url = Column(String, nullable=True)
    documentation_url = Column(String, nullable=True)
    stars = Column(Integer, nullable=True)
    language = Column(String, nullable=True)  # javascript, python, go, etc.
    confidence_score = Column(Float, nullable=False, default=0.0)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    last_updated = Column(DateTime(timezone=True), server_default=func.now())


class MCPCategory(Base):
    __tablename__ = "mcp_categories"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False, unique=True)
    description = Column(Text, nullable=True)
    icon = Column(String, nullable=True)  # Icon name or emoji
    color = Column(String, nullable=True)  # Hex color code
    created_at = Column(DateTime(timezone=True), server_default=func.now())