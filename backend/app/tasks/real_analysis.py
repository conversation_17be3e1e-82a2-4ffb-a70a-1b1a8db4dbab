"""
Real repository analysis task with GitHub API integration
"""
from celery import current_task
from sqlalchemy.orm import Session
from datetime import datetime
import logging
import requests
import json
from typing import Dict, Any, List, Optional
import re

from .celery_app import celery_app
from ..database import SessionLocal
from ..models import RepoAnalysis

logger = logging.getLogger(__name__)


@celery_app.task(bind=True, max_retries=3)
def analyze_repository_real(self, analysis_id: int, github_token: str):
    """
    Real repository analysis task that fetches data from GitHub API
    """
    db = SessionLocal()
    try:
        # Get analysis record
        analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()
        if not analysis:
            raise ValueError(f"Analysis {analysis_id} not found")
        
        # Update status to analyzing
        analysis.status = "analyzing" 
        db.commit()
        
        # Step 1: Get repository info
        self.update_state(
            state='PROGRESS',
            meta={'current': 10, 'total': 100, 'status': 'Fetching repository information...'}
        )
        
        repo_info = get_repository_info(analysis.repo_owner, analysis.repo_name, github_token)
        
        # Step 2: Analyze repository structure
        self.update_state(
            state='PROGRESS', 
            meta={'current': 30, 'total': 100, 'status': 'Analyzing repository structure...'}
        )
        
        languages = get_repository_languages(analysis.repo_owner, analysis.repo_name, github_token)
        
        # Step 3: Get repository structure
        self.update_state(
            state='PROGRESS',
            meta={'current': 40, 'total': 100, 'status': 'Building repository structure...'}
        )
        
        repo_tree = get_repository_tree(analysis.repo_owner, analysis.repo_name, github_token, max_depth=3)
        
        # Step 4: Analyze dependencies
        self.update_state(
            state='PROGRESS',
            meta={'current': 50, 'total': 100, 'status': 'Analyzing dependencies...'}
        )
        
        contents = get_repository_contents(analysis.repo_owner, analysis.repo_name, github_token)
        dependencies_analysis = analyze_dependencies_from_github(analysis.repo_owner, analysis.repo_name, github_token, contents)
        
        # Step 5: Detect API endpoints and patterns  
        self.update_state(
            state='PROGRESS',
            meta={'current': 65, 'total': 100, 'status': 'Detecting API endpoints...'}
        )
        
        api_endpoints = analyze_for_api_endpoints(contents, repo_info.get('language', ''), analysis.repo_owner, analysis.repo_name, github_token)
        
        # Step 6: Calculate MCP feasibility score
        self.update_state(
            state='PROGRESS',
            meta={'current': 75, 'total': 100, 'status': 'Calculating MCP feasibility score...'}
        )
        
        mcp_score = calculate_mcp_score(repo_info, languages, api_endpoints, contents)
        
        # Step 7: Generate analysis results
        self.update_state(
            state='PROGRESS',
            meta={'current': 85, 'total': 100, 'status': 'Generating analysis results...'}
        )
        
        # Enhanced code pattern analysis with real code examination
        code_patterns = analyze_code_patterns(repo_info, contents, repo_info.get('language', ''), analysis.repo_owner, analysis.repo_name, github_token)
        
        analysis_results = {
            "mcp_feasibility_score": mcp_score,
            "repository_info": {
                "name": repo_info.get('name'),
                "description": repo_info.get('description'),
                "language": repo_info.get('language'),
                "size": repo_info.get('size'),
                "stars": repo_info.get('stargazers_count', 0),
                "forks": repo_info.get('forks_count', 0),
                "url": repo_info.get('html_url'),
                "owner": repo_info.get('owner', {}).get('login'),
                "created_at": repo_info.get('created_at'),
                "updated_at": repo_info.get('updated_at')
            },
            "languages": languages,
            "dependencies": dependencies_analysis,
            "api_endpoints": api_endpoints,
            "repository_tree": repo_tree,
            "code_structure": analyze_code_structure(contents, languages),
            "code_patterns": code_patterns,
            "mcp_capabilities": generate_mcp_capabilities(api_endpoints, repo_info),
            "recommendations": generate_enhanced_recommendations(mcp_score, api_endpoints, repo_info, code_patterns)
        }
        
        # Debug logging
        logger.info(f"Repository tree structure for analysis {analysis_id}:")
        logger.info(f"Tree name: {repo_tree.get('name')}")
        logger.info(f"Tree children count: {len(repo_tree.get('children', []))}")
        logger.info(f"MCP indicators: {repo_tree.get('mcp_indicators', {})}")
        
        # Step 8: Save results
        self.update_state(
            state='PROGRESS',
            meta={'current': 95, 'total': 100, 'status': 'Saving results...'}
        )
        
        analysis.status = "completed"
        analysis.mcp_feasibility_score = mcp_score
        analysis.analysis_results = analysis_results
        analysis.completed_at = datetime.utcnow()
        analysis.error_message = None
        db.commit()
        
        return {
            "analysis_id": analysis_id,
            "status": "completed", 
            "mcp_score": mcp_score,
            "repository": analysis.repo_name
        }
        
    except Exception as e:
        error_msg = str(e)
        logger.error(f"Analysis failed for {analysis_id}: {error_msg}")
        
        # Update analysis with error
        analysis.status = "failed"
        analysis.error_message = error_msg
        analysis.completed_at = datetime.utcnow()
        db.commit()
        
        # Retry on GitHub rate limit or temporary errors
        if "rate limit" in error_msg.lower() or "502" in error_msg or "503" in error_msg:
            self.retry(countdown=60, exc=e)
        
        raise
        
    finally:
        db.close()


def get_repository_info(owner: str, repo: str, github_token: str) -> Dict[str, Any]:
    """Get repository information from GitHub API"""
    url = f"https://api.github.com/repos/{owner}/{repo}"
    headers = {
        "Authorization": f"token {github_token}",
        "Accept": "application/vnd.github.v3+json",
        "User-Agent": "SuperMCP/1.0"
    }
    
    response = requests.get(url, headers=headers, timeout=30)
    if response.status_code == 401:
        raise Exception("Bad credentials - GitHub token is invalid or expired")
    elif response.status_code == 404:
        raise Exception("Repository not found or not accessible")
    elif response.status_code != 200:
        raise Exception(f"GitHub API error: {response.status_code} - {response.text}")
    
    return response.json()


def get_repository_languages(owner: str, repo: str, github_token: str) -> Dict[str, int]:
    """Get repository languages from GitHub API"""
    url = f"https://api.github.com/repos/{owner}/{repo}/languages"
    headers = {
        "Authorization": f"token {github_token}",
        "Accept": "application/vnd.github.v3+json",
        "User-Agent": "SuperMCP/1.0"
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=30)
        if response.status_code == 200:
            return response.json()
    except Exception as e:
        logger.warning(f"Failed to get languages: {e}")
    return {}


def get_repository_contents(owner: str, repo: str, github_token: str, path: str = "") -> List[Dict[str, Any]]:
    """Get repository contents from GitHub API"""
    url = f"https://api.github.com/repos/{owner}/{repo}/contents/{path}"
    headers = {
        "Authorization": f"token {github_token}",
        "Accept": "application/vnd.github.v3+json", 
        "User-Agent": "SuperMCP/1.0"
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=30)
        if response.status_code == 200:
            return response.json()
    except Exception as e:
        logger.warning(f"Failed to get contents: {e}")
    return []


def get_repository_tree(owner: str, repo: str, github_token: str, max_depth: int = 3) -> Dict[str, Any]:
    """Get repository file tree structure recursively"""
    def fetch_tree_recursive(path: str = "", current_depth: int = 0) -> List[Dict[str, Any]]:
        if current_depth >= max_depth:
            return []
            
        contents = get_repository_contents(owner, repo, github_token, path)
        tree = []
        
        for item in contents[:50]:  # Limit to 50 items per directory
            tree_item = {
                "name": item.get("name"),
                "path": item.get("path"),
                "type": item.get("type"),  # "file" or "dir"
                "size": item.get("size", 0),
                "url": item.get("html_url")
            }
            
            # For directories, recursively get children if within depth limit
            if item.get("type") == "dir" and current_depth < max_depth - 1:
                tree_item["children"] = fetch_tree_recursive(item.get("path", ""), current_depth + 1)
            elif item.get("type") == "dir":
                tree_item["children"] = []  # Empty children array for collapsed state
                
            tree.append(tree_item)
            
        return tree
    
    try:
        # Get repository info for root context
        repo_info = get_repository_info(owner, repo, github_token)
        
        return {
            "name": repo_info.get("name", repo),
            "path": "",
            "type": "dir",
            "size": repo_info.get("size", 0),
            "children": fetch_tree_recursive(),
            "total_files": len([item for item in fetch_tree_recursive() if item.get("type") == "file"]),
            "mcp_indicators": analyze_mcp_indicators(fetch_tree_recursive())
        }
    except Exception as e:
        logger.error(f"Failed to get repository tree: {e}")
        return {
            "name": repo,
            "path": "",
            "type": "dir", 
            "children": [],
            "total_files": 0,
            "mcp_indicators": {}
        }


def analyze_mcp_indicators(tree: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Analyze tree structure for MCP potential indicators"""
    indicators = {
        "has_api_routes": False,
        "has_config_files": False,
        "has_package_json": False,
        "has_requirements_txt": False,
        "has_docker": False,
        "has_readme": False,
        "api_files": [],
        "config_files": [],
        "key_files": []
    }
    
    def scan_tree(items: List[Dict[str, Any]], path_prefix: str = ""):
        for item in items:
            name = item.get("name", "").lower()
            full_path = f"{path_prefix}/{name}" if path_prefix else name
            
            # API route indicators
            if any(keyword in name for keyword in ["route", "api", "endpoint", "controller"]):
                indicators["has_api_routes"] = True
                indicators["api_files"].append(full_path)
            
            # Configuration files
            if name in ["package.json", "requirements.txt", "composer.json", "pom.xml", "cargo.toml"]:
                indicators["has_config_files"] = True
                indicators["config_files"].append(full_path)
                if name == "package.json":
                    indicators["has_package_json"] = True
                elif name == "requirements.txt":
                    indicators["has_requirements_txt"] = True
            
            # Docker files
            if name in ["dockerfile", "docker-compose.yml", "docker-compose.yaml"]:
                indicators["has_docker"] = True
                indicators["key_files"].append(full_path)
            
            # README files
            if name.startswith("readme"):
                indicators["has_readme"] = True
                indicators["key_files"].append(full_path)
                
            # Recursively scan children
            if item.get("children"):
                scan_tree(item["children"], full_path)
    
    scan_tree(tree)
    return indicators


def analyze_dependencies_from_github(owner: str, repo: str, github_token: str, contents: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Analyze dependencies by fetching and parsing dependency files from GitHub"""
    dependencies = {
        "total_dependencies": 0,
        "by_language": {},
        "by_type": {},
        "key_dependencies": [],
        "dependency_files": []
    }
    
    # Find dependency files
    dependency_files = [
        item for item in contents if item.get('type') == 'file' and 
        item.get('name', '').lower() in [
            'requirements.txt', 'package.json', 'composer.json', 'pom.xml', 
            'cargo.toml', 'go.mod', 'gemfile', 'setup.py', 'pyproject.toml'
        ]
    ]
    
    logger.info(f"Found {len(dependency_files)} dependency files")
    
    for dep_file in dependency_files:
        file_name = dep_file.get('name', '').lower()
        file_path = dep_file.get('path', '')
        
        content = get_file_content_from_github(owner, repo, github_token, file_path)
        if not content:
            continue
            
        dependencies["dependency_files"].append({
            "name": file_name,
            "path": file_path,
            "size": dep_file.get('size', 0)
        })
        
        parsed_deps = []
        
        try:
            if file_name == 'requirements.txt':
                parsed_deps = parse_requirements_txt(content)
                dependencies["by_language"]["python"] = len(parsed_deps)
                
            elif file_name == 'package.json':
                parsed_deps = parse_package_json(content)
                dependencies["by_language"]["javascript"] = len(parsed_deps)
                
            elif file_name == 'composer.json':
                parsed_deps = parse_composer_json(content)
                dependencies["by_language"]["php"] = len(parsed_deps)
                
            elif file_name == 'pom.xml':
                parsed_deps = parse_pom_xml(content)
                dependencies["by_language"]["java"] = len(parsed_deps)
                
            elif file_name == 'cargo.toml':
                parsed_deps = parse_cargo_toml(content)
                dependencies["by_language"]["rust"] = len(parsed_deps)
                
            elif file_name == 'go.mod':
                parsed_deps = parse_go_mod(content)
                dependencies["by_language"]["go"] = len(parsed_deps)
                
            dependencies["key_dependencies"].extend(parsed_deps)
            dependencies["total_dependencies"] += len(parsed_deps)
            
        except Exception as e:
            logger.warning(f"Error parsing {file_name}: {e}")
    
    # Categorize dependencies by type
    for dep in dependencies["key_dependencies"]:
        dep_type = dep.get("type", "unknown")
        dependencies["by_type"][dep_type] = dependencies["by_type"].get(dep_type, 0) + 1
    
    logger.info(f"Analyzed {dependencies['total_dependencies']} dependencies across {len(dependencies['by_language'])} languages")
    return dependencies


def parse_requirements_txt(content: str) -> List[Dict[str, Any]]:
    """Parse Python requirements.txt file"""
    deps = []
    for line in content.split('\n'):
        line = line.strip()
        if line and not line.startswith('#') and not line.startswith('-'):
            # Simple parsing: package_name>=version
            import re
            match = re.match(r'^([a-zA-Z0-9_-]+)([><=!]+)?([^;#\s]+)?', line)
            if match:
                name = match.group(1)
                version = match.group(3) if match.group(3) else "latest"
                deps.append({
                    "name": name,
                    "version": version,
                    "type": "runtime",
                    "language": "python"
                })
    return deps


def parse_package_json(content: str) -> List[Dict[str, Any]]:
    """Parse Node.js package.json file"""
    deps = []
    try:
        import json
        data = json.loads(content)
        
        # Regular dependencies
        for name, version in data.get('dependencies', {}).items():
            deps.append({
                "name": name,
                "version": version,
                "type": "runtime",
                "language": "javascript"
            })
            
        # Dev dependencies
        for name, version in data.get('devDependencies', {}).items():
            deps.append({
                "name": name,
                "version": version,
                "type": "development",
                "language": "javascript"
            })
    except Exception as e:
        logger.warning(f"Error parsing package.json: {e}")
    return deps


def parse_composer_json(content: str) -> List[Dict[str, Any]]:
    """Parse PHP composer.json file"""
    deps = []
    try:
        import json
        data = json.loads(content)
        
        for name, version in data.get('require', {}).items():
            deps.append({
                "name": name,
                "version": version,
                "type": "runtime",
                "language": "php"
            })
    except Exception as e:
        logger.warning(f"Error parsing composer.json: {e}")
    return deps


def parse_pom_xml(content: str) -> List[Dict[str, Any]]:
    """Parse Java pom.xml file (basic regex parsing)"""
    deps = []
    import re
    
    # Simple regex to find dependencies
    dep_pattern = r'<dependency>.*?<groupId>(.*?)</groupId>.*?<artifactId>(.*?)</artifactId>.*?<version>(.*?)</version>.*?</dependency>'
    matches = re.findall(dep_pattern, content, re.DOTALL)
    
    for group_id, artifact_id, version in matches:
        deps.append({
            "name": f"{group_id}:{artifact_id}",
            "version": version.strip(),
            "type": "runtime",
            "language": "java"
        })
    return deps


def parse_cargo_toml(content: str) -> List[Dict[str, Any]]:
    """Parse Rust Cargo.toml file (basic parsing)"""
    deps = []
    import re
    
    # Find [dependencies] section
    deps_section = re.search(r'\[dependencies\](.*?)(?=\[[^\]]*\]|$)', content, re.DOTALL)
    if deps_section:
        for line in deps_section.group(1).split('\n'):
            line = line.strip()
            if '=' in line and not line.startswith('#'):
                name = line.split('=')[0].strip()
                version = line.split('=')[1].strip().strip('"\'')
                deps.append({
                    "name": name,
                    "version": version,
                    "type": "runtime",
                    "language": "rust"
                })
    return deps


def parse_go_mod(content: str) -> List[Dict[str, Any]]:
    """Parse Go go.mod file"""
    deps = []
    import re
    
    # Find require block
    require_pattern = r'require\s*\((.*?)\)'
    require_match = re.search(require_pattern, content, re.DOTALL)
    
    if require_match:
        for line in require_match.group(1).split('\n'):
            line = line.strip()
            if line and not line.startswith('//'):
                parts = line.split()
                if len(parts) >= 2:
                    deps.append({
                        "name": parts[0],
                        "version": parts[1],
                        "type": "runtime",
                        "language": "go"
                    })
    return deps


def get_file_content_from_github(owner: str, repo: str, github_token: str, file_path: str) -> Optional[str]:
    """Get actual file content from GitHub API"""
    url = f"https://api.github.com/repos/{owner}/{repo}/contents/{file_path}"
    headers = {
        "Authorization": f"token {github_token}",
        "Accept": "application/vnd.github.v3+json",
        "User-Agent": "SuperMCP/1.0"
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=30)
        if response.status_code == 200:
            file_data = response.json()
            if file_data.get('encoding') == 'base64':
                import base64
                content = base64.b64decode(file_data['content']).decode('utf-8', errors='ignore')
                return content
    except Exception as e:
        logger.warning(f"Failed to get file content for {file_path}: {e}")
    return None


def analyze_for_api_endpoints(contents: List[Dict[str, Any]], language: str, owner: str = "", repo: str = "", github_token: str = "") -> List[Dict[str, Any]]:
    """Analyze repository contents for REAL API endpoints and functions by examining actual file contents"""
    endpoints = []
    
    # API patterns to search for in actual code
    api_patterns = {
        "Python": [
            (r"@app\.route\(['\"]([^'\"]+)['\"].*methods=\[['\"]([^'\"]+)['\"]", "Flask route"),
            (r"@router\.(get|post|put|delete)\(['\"]([^'\"]+)['\"]", "FastAPI router"),
            (r"@app\.(get|post|put|delete)\(['\"]([^'\"]+)['\"]", "FastAPI app"),
            (r"def\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\([^)]*\):", "Function definition"),
        ],
        "JavaScript": [
            (r"app\.(get|post|put|delete)\(['\"]([^'\"]+)['\"]", "Express route"),
            (r"router\.(get|post|put|delete)\(['\"]([^'\"]+)['\"]", "Express router"),
            (r"\.route\(['\"]([^'\"]+)['\"]", "Route definition"),
            (r"export\s+(?:async\s+)?function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)", "Exported function"),
        ],
        "TypeScript": [
            (r"@(Get|Post|Put|Delete)\(['\"]([^'\"]+)['\"]", "NestJS decorator"),
            (r"app\.(get|post|put|delete)\(['\"]([^'\"]+)['\"]", "Express route"),
            (r"export\s+(?:async\s+)?function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)", "Exported function"),
        ]
    }
    
    patterns_for_lang = api_patterns.get(language, api_patterns.get("Python", []))
    
    # Analyze actual file contents for API patterns
    api_files = [item for item in contents if item.get('type') == 'file' and 
                 any(keyword in item.get('name', '').lower() for keyword in 
                     ['api', 'route', 'controller', 'endpoint', 'handler', 'server', 'app'])]
    
    logger.info(f"Found {len(api_files)} potential API files to analyze")
    
    for api_file in api_files[:10]:  # Limit to 10 files to avoid rate limits
        file_path = api_file.get('path', '')
        file_name = api_file.get('name', '')
        
        # Skip non-code files
        if not any(file_name.endswith(ext) for ext in ['.py', '.js', '.ts', '.jsx', '.tsx']):
            continue
            
        logger.info(f"Analyzing file: {file_path}")
        
        # Get actual file content from GitHub
        if github_token and owner and repo:
            content = get_file_content_from_github(owner, repo, github_token, file_path)
            if content:
                # Search for API patterns in the actual code
                for pattern_regex, pattern_type in patterns_for_lang:
                    matches = re.finditer(pattern_regex, content, re.MULTILINE | re.IGNORECASE)
                    for match in matches:
                        groups = match.groups()
                        if len(groups) >= 2:
                            path = groups[1] if len(groups) > 1 else groups[0]
                            method = groups[0].upper() if groups[0].lower() in ['get', 'post', 'put', 'delete'] else 'GET'
                        else:
                            path = f"/{groups[0]}" if groups else f"/api/{file_name.split('.')[0]}"
                            method = "GET"
                        
                        # Extract function name from surrounding context
                        function_name = groups[0] if pattern_type == "Function definition" else f"{file_name.split('.')[0]}_{method.lower()}"
                        
                        endpoint = {
                            "path": path,
                            "method": method,
                            "description": f"{pattern_type} from {file_name}",
                            "function_name": function_name,
                            "file_path": file_path,
                            "mcp_potential": "high" if any(keyword in path.lower() for keyword in ['api', 'data', 'user', 'auth']) else "medium",
                            "suggested_tool": f"{function_name.replace('/', '_').replace('-', '_')}_tool",
                            "source": "real_analysis"
                        }
                        endpoints.append(endpoint)
                        
                        # Limit endpoints per file
                        if len(endpoints) >= 20:
                            break
                    
                    if len(endpoints) >= 20:
                        break
            else:
                logger.warning(f"Could not fetch content for {file_path}")
    
    # If no real endpoints found, analyze file names and structure for potential APIs
    if not endpoints:
        logger.info("No API patterns found in code, analyzing file structure...")
        for item in contents[:20]:  # Analyze first 20 items
            name = item.get('name', '').lower()
            if item.get('type') == 'file' and any(keyword in name for keyword in ['api', 'route', 'handler', 'controller']):
                endpoints.append({
                    "path": f"/api/{name.split('.')[0]}",
                    "method": "GET",
                    "description": f"Potential API endpoint based on file: {item.get('name')}",
                    "function_name": name.split('.')[0],
                    "file_path": item.get('path', ''),
                    "mcp_potential": "medium",
                    "suggested_tool": f"{name.split('.')[0]}_tool",
                    "source": "structure_analysis"
                })
    
    logger.info(f"Found {len(endpoints)} API endpoints through real analysis")
    return endpoints[:25]  # Limit to 25 endpoints


def analyze_code_patterns(repo_info: Dict[str, Any], contents: List[Dict[str, Any]], 
                         language: str, owner: str = "", repo: str = "", github_token: str = "") -> Dict[str, Any]:
    """Analyze REAL code patterns by examining actual file contents and structure"""
    patterns = {
        "library_type": "unknown",
        "main_functionality": [],
        "suggested_tools": [],
        "architecture_patterns": [],
        "use_cases": []
    }
    
    repo_name = repo_info.get('name', '').lower()
    description = (repo_info.get('description') or '').lower()
    detected_functions = []
    detected_classes = []
    config_files = []
    
    logger.info(f"Analyzing code patterns for {len(contents)} files")
    
    # Analyze key files for real patterns
    key_files = [item for item in contents if item.get('type') == 'file' and 
                 any(item.get('name', '').lower().endswith(ext) for ext in 
                     ['.py', '.js', '.ts', '.jsx', '.tsx', '.go', '.java', '.rs', '.php', '.rb', '.cs'])]
    
    # Also look for configuration and documentation files
    config_files = [item for item in contents if item.get('type') == 'file' and 
                   any(item.get('name', '').lower() in name for name in 
                       ['package.json', 'requirements.txt', 'setup.py', 'pyproject.toml', 'cargo.toml', 
                        'pom.xml', 'composer.json', 'gemfile', 'go.mod', 'readme.md', 'docs'])]
    
    # Analyze actual code files for patterns
    functions_found = []
    classes_found = []
    imports_found = []
    
    for code_file in key_files[:8]:  # Limit to 8 files to avoid rate limits
        file_path = code_file.get('path', '')
        file_name = code_file.get('name', '')
        
        if github_token and owner and repo:
            content = get_file_content_from_github(owner, repo, github_token, file_path)
            if content and isinstance(content, str):
                try:
                    # Extract functions, classes, and imports from actual code
                    if file_name.endswith('.py'):
                        functions_found.extend(re.findall(r'def\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(', content))
                        classes_found.extend(re.findall(r'class\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*[\(:]', content))
                        imports_found.extend(re.findall(r'(?:from\s+([a-zA-Z_][a-zA-Z0-9_.]*)|import\s+([a-zA-Z_][a-zA-Z0-9_.]*))', content))
                    elif file_name.endswith(('.js', '.ts', '.jsx', '.tsx')):
                        functions_found.extend(re.findall(r'(?:function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)|const\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=\s*(?:async\s+)?(?:function|\([^)]*\)\s*=>))', content))
                        classes_found.extend(re.findall(r'class\s+([a-zA-Z_$][a-zA-Z0-9_$]*)', content))
                        imports_found.extend(re.findall(r'import.*from\s+[\'"]([^\'"]+)[\'"]', content))
                except Exception as e:
                    logger.warning(f"Error parsing code patterns from {file_path}: {e}")
    
    # Flatten tuples from regex findall results
    functions_found = [f for group in functions_found for f in group if f]
    classes_found = [c for group in classes_found for c in group if c]  
    imports_found = [i for group in imports_found for i in group if i]
    
    logger.info(f"Found {len(functions_found)} functions, {len(classes_found)} classes, {len(imports_found)} imports")
    
    # Determine library type based on REAL analysis
    library_type = "general_utility"
    main_functionality = []
    suggested_tools = []
    architecture_patterns = []
    use_cases = []
    
    # Analyze based on actual imports and file structure
    ml_imports = [imp for imp in imports_found if any(ml_lib in imp.lower() for ml_lib in 
                 ['tensorflow', 'torch', 'sklearn', 'pandas', 'numpy', 'transformers', 'openai'])]
    
    web_imports = [imp for imp in imports_found if any(web_lib in imp.lower() for web_lib in 
                  ['fastapi', 'flask', 'django', 'express', 'react', 'vue', 'next'])]
    
    data_imports = [imp for imp in imports_found if any(data_lib in imp.lower() for data_lib in 
                   ['pandas', 'numpy', 'requests', 'urllib', 'json', 'csv', 'database', 'sqlalchemy'])]
    
    # Real pattern detection based on code analysis
    if ml_imports or any(keyword in description for keyword in ['ml', 'ai', 'model', 'neural', 'learning']):
        library_type = "ml_ai"
        main_functionality = [f"Machine learning functionality using {', '.join(ml_imports[:3])}" if ml_imports else "AI/ML operations",
                            "Data processing and model inference", 
                            "Automated predictions and analysis"]
        
        # Generate tools based on actual functions found
        for func in functions_found[:5]:
            if any(keyword in func.lower() for keyword in ['predict', 'infer', 'model', 'train', 'process']):
                suggested_tools.append({
                    "name": func,
                    "description": f"Execute {func} function for ML operations",
                    "input_schema": {"data": "object", "config": "object"},
                    "use_cases": ["Model inference", "Data processing", "ML automation"]
                })
        
        architecture_patterns = ["Model loading and caching", "Input validation", "Batch processing support"]
        use_cases = ["Automated ML predictions", "Data analysis workflows", "AI-powered applications"]
        
    elif web_imports or any(keyword in description for keyword in ['api', 'web', 'server', 'http']):
        library_type = "web_api"
        main_functionality = [f"Web API functionality using {', '.join(web_imports[:3])}" if web_imports else "Web service operations",
                            "HTTP request/response handling",
                            "API endpoint management"]
        
        # Generate tools based on actual functions
        for func in functions_found[:5]:
            if any(keyword in func.lower() for keyword in ['get', 'post', 'api', 'request', 'handle']):
                suggested_tools.append({
                    "name": func,
                    "description": f"Execute {func} API function",
                    "input_schema": {"endpoint": "string", "data": "object", "headers": "object"},
                    "use_cases": ["API integration", "Web automation", "Service integration"]
                })
                
        architecture_patterns = ["RESTful API design", "Authentication handling", "Error response formatting"]
        use_cases = ["API integration", "Web service automation", "Microservice orchestration"]
        
    elif data_imports or any(keyword in description for keyword in ['data', 'process', 'parse', 'extract']):
        library_type = "data_processing"
        main_functionality = [f"Data processing using {', '.join(data_imports[:3])}" if data_imports else "Data manipulation operations",
                            "File parsing and extraction",
                            "Data transformation workflows"]
        
        for func in functions_found[:5]:
            if any(keyword in func.lower() for keyword in ['parse', 'extract', 'process', 'read', 'load']):
                suggested_tools.append({
                    "name": func,
                    "description": f"Execute {func} for data processing",
                    "input_schema": {"input_data": "string", "options": "object"},
                    "use_cases": ["Data extraction", "File processing", "ETL operations"]
                })
                
        architecture_patterns = ["Stream processing", "Error handling for malformed data", "Memory-efficient parsing"]
        use_cases = ["Data pipeline automation", "File processing workflows", "ETL operations"]
        
    else:
        # Fallback: analyze based on actual functions found
        library_type = "utility_library"
        main_functionality = [f"Core functionality with {len(functions_found)} functions" if functions_found else "Utility operations",
                            f"Class-based architecture with {len(classes_found)} classes" if classes_found else "Function-based operations"]
        
        # Use actual function names for tools
        for func in functions_found[:3]:
            suggested_tools.append({
                "name": func,
                "description": f"Execute {func} function",
                "input_schema": {"args": "array", "kwargs": "object"},
                "use_cases": ["Function execution", "Library integration", "Automation"]
            })
            
        architecture_patterns = ["Function composition", "Error handling", "Input validation"]
        use_cases = ["Library integration", "Utility automation", "Custom workflows"]
    
    patterns.update({
        "library_type": library_type,
        "main_functionality": main_functionality,
        "suggested_tools": suggested_tools[:5],  # Limit to 5 tools
        "architecture_patterns": architecture_patterns,
        "use_cases": use_cases,
        "detected_functions": functions_found[:10],  # Include actual functions found
        "detected_classes": classes_found[:5],
        "key_imports": imports_found[:10],
        "config_files": [f.get('name') for f in config_files]
    })
    
    logger.info(f"Detected library type: {library_type}, {len(suggested_tools)} tools")
    return patterns


def calculate_mcp_score(repo_info: Dict[str, Any], languages: Dict[str, int], 
                       endpoints: List[Dict[str, Any]], contents: List[Dict[str, Any]]) -> float:
    """Calculate MCP feasibility score based on repository analysis"""
    score = 0.0
    
    # Base score from repository characteristics
    if repo_info.get('stargazers_count', 0) > 100:
        score += 10
    if repo_info.get('size', 0) > 1000:  # KB
        score += 5
    
    # Language bonus
    primary_language = repo_info.get('language', '').lower()
    if primary_language in ['python', 'javascript', 'typescript']:
        score += 15
    
    # API endpoints bonus
    score += min(len(endpoints) * 5, 25)  # Max 25 points for endpoints
    
    # Multi-language bonus
    if len(languages) > 1:
        score += 5
    
    # Repository activity bonus
    if repo_info.get('updated_at'):
        score += 10
    
    # Structure analysis bonus (simulated)
    score += 20  # Base structure score
    
    return min(score, 100.0)


def analyze_code_structure(contents: List[Dict[str, Any]], languages: Dict[str, int]) -> Dict[str, Any]:
    """Analyze code structure and patterns"""
    return {
        "file_count": len(contents),
        "languages": languages,
        "has_api": len(contents) > 0,  # Simplified
        "has_cli": any("main" in str(item.get('name', '')).lower() for item in contents),
        "has_database": any("model" in str(item.get('name', '')).lower() for item in contents),
        "has_tests": any("test" in str(item.get('name', '')).lower() for item in contents)
    }


def generate_mcp_capabilities(endpoints: List[Dict[str, Any]], repo_info: Dict[str, Any]) -> Dict[str, Any]:
    """Generate MCP capabilities based on analysis"""
    tools = []
    for endpoint in endpoints:
        tool_name = endpoint['path'].replace('/', '_').replace('-', '_').strip('_')
        if tool_name:
            tools.append({
                "name": tool_name,
                "description": endpoint.get('description', f"{endpoint['method']} {endpoint['path']}")
            })
    
    return {
        "tools": tools,
        "resources": [],
        "prompts": [],
        "server_info": {
            "name": repo_info.get('name', 'unknown'),
            "mcp_score": calculate_mcp_score(repo_info, {}, endpoints, [])
        }
    }


def generate_recommendations(mcp_score: float, endpoints: List[Dict[str, Any]], 
                           repo_info: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Generate implementation recommendations"""
    recommendations = []
    
    if mcp_score >= 70:
        recommendations.append({
            "priority": "high",
            "title": "Excellent MCP Server Candidate",
            "description": "This repository is well-suited for MCP server implementation",
            "effort": "low",
            "timeline": "1-2 weeks"
        })
    elif mcp_score >= 50:
        recommendations.append({
            "priority": "medium", 
            "title": "Good MCP Potential with Enhancements",
            "description": "Consider adding more structured APIs or documentation",
            "effort": "medium",
            "timeline": "2-3 weeks"
        })
    else:
        recommendations.append({
            "priority": "low",
            "title": "Requires Significant Development",
            "description": "Repository needs substantial API development for effective MCP integration",
            "effort": "high", 
            "timeline": "4+ weeks"
        })
    
    return recommendations


def generate_enhanced_recommendations(mcp_score: float, endpoints: List[Dict[str, Any]], 
                                    repo_info: Dict[str, Any], code_patterns: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Generate detailed, context-aware MCP implementation recommendations"""
    recommendations = []
    
    repo_name = repo_info.get('name', 'Unknown Repository')
    library_type = code_patterns.get('library_type', 'unknown')
    suggested_tools = code_patterns.get('suggested_tools', [])
    
    # Primary feasibility assessment with detailed reasoning
    if mcp_score >= 70:
        recommendations.append({
            "type": "feasibility",
            "priority": "high",
            "title": f"Excellent MCP Server Candidate: {repo_name}",
            "description": f"Building an MCP server for {repo_name} has **excellent feasibility**. " +
                          f"This {library_type.replace('_', ' ')} library provides clean APIs that translate well to MCP tools.",
            "reasoning": [
                "Well-defined, stateless functions perfect for tool interfaces",
                "Valuable functionality that AI assistants frequently need", 
                "Handles complex operations internally (rate limiting, retries, etc.)",
                "Returns structured, parseable outputs",
                "Clear input/output patterns"
            ],
            "effort": "low",
            "timeline": "1-2 weeks",
            "confidence": "95%"
        })
    elif mcp_score >= 50:
        recommendations.append({
            "type": "feasibility",
            "priority": "medium", 
            "title": f"Good MCP Potential: {repo_name}",
            "description": f"Building an MCP server for {repo_name} has **good feasibility** with some enhancements needed.",
            "reasoning": [
                "Core functionality is suitable for MCP tools",
                "May need additional structure or documentation",
                "Some API patterns could be better defined",
                "Good foundation for MCP implementation"
            ],
            "effort": "medium",
            "timeline": "2-3 weeks",
            "confidence": "75%"
        })
    else:
        recommendations.append({
            "type": "feasibility",
            "priority": "low",
            "title": f"Requires Development: {repo_name}",
            "description": f"Building an MCP server for {repo_name} requires significant additional development.",
            "reasoning": [
                "Limited structured API surface",
                "May need substantial refactoring for MCP compatibility",
                "Consider enhancing the core library first",
                "Focus on creating clear, stateless interfaces"
            ],
            "effort": "high", 
            "timeline": "4+ weeks",
            "confidence": "45%"
        })
    
    # Detailed tool recommendations
    if suggested_tools:
        tool_recommendations = {
            "type": "implementation",
            "priority": "high",
            "title": "Recommended MCP Server APIs",
            "description": f"Based on analysis of {repo_name}, here are the key APIs you should expose through your MCP server:",
            "tools": suggested_tools,
            "effort": "medium",
            "timeline": "1-2 weeks per tool category"
        }
        recommendations.append(tool_recommendations)
    
    # Architecture and implementation recommendations
    architecture_patterns = code_patterns.get('architecture_patterns', [])
    if architecture_patterns:
        recommendations.append({
            "type": "architecture",
            "priority": "medium",
            "title": "Implementation Considerations",
            "description": "Architectural patterns and best practices for your MCP server implementation:",
            "patterns": architecture_patterns,
            "effort": "medium",
            "timeline": "Ongoing during development"
        })
    
    # Use case recommendations
    use_cases = code_patterns.get('use_cases', [])
    if use_cases:
        recommendations.append({
            "type": "use_cases",
            "priority": "low",
            "title": "Potential Use Cases",
            "description": f"This MCP server would be valuable for AI assistants that need to:",
            "use_cases": use_cases,
            "effort": "N/A",
            "timeline": "Post-implementation"
        })
    
    # Sample MCP tool definition
    if suggested_tools:
        primary_tool = suggested_tools[0]
        sample_tool = {
            "type": "sample_implementation", 
            "priority": "medium",
            "title": "Sample MCP Tool Definition",
            "description": f"Example MCP tool definition for the primary '{primary_tool['name']}' function:",
            "tool_definition": {
                "name": primary_tool['name'],
                "description": primary_tool['description'],
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        key: {"type": value.split('[')[0], "description": f"Input parameter: {key}"}
                        for key, value in primary_tool.get('input_schema', {}).items()
                    },
                    "required": list(primary_tool.get('input_schema', {}).keys())[:2]  # First 2 params required
                }
            },
            "effort": "low",
            "timeline": "1-2 days per tool"
        }
        recommendations.append(sample_tool)
    
    return recommendations