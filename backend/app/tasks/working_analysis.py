"""
Working analysis task without complex dependencies
"""
from celery import current_task
from sqlalchemy.orm import Session
from datetime import datetime
import logging
import time

from .celery_app import celery_app
from ..database import SessionLocal
from ..models import RepoAnalysis

logger = logging.getLogger(__name__)


@celery_app.task(bind=True)
def simple_working_analysis(self, analysis_id: int, github_token: str):
    """
    Simple working analysis task for demonstration
    """
    try:
        # Simulate analysis progress
        for i in range(0, 101, 20):
            self.update_state(
                state='PROGRESS',
                meta={'current': i, 'total': 100, 'status': f'Processing... {i}%'}
            )
            time.sleep(2)
        
        # Update database
        db = SessionLocal()
        try:
            analysis = db.query(RepoAnalysis).filter(RepoAnalysis.id == analysis_id).first()
            if analysis:
                analysis.status = "completed"
                analysis.mcp_feasibility_score = 75.5
                analysis.analysis_results = {
                    "mcp_feasibility_score": 75.5,
                    "api_endpoints": [
                        {"path": "/users", "method": "GET", "description": "List users"},
                        {"path": "/data", "method": "POST", "description": "Process data"}
                    ],
                    "repository_info": {
                        "name": analysis.repo_name,
                        "language": "Python",
                        "description": "Sample repository analysis"
                    },
                    "mcp_capabilities": {
                        "tools": [
                            {"name": "get_users", "description": "Get user list"}
                        ],
                        "resources": [],
                        "prompts": [],
                        "server_info": {
                            "name": analysis.repo_name,
                            "mcp_score": 75.5
                        }
                    }
                }
                analysis.completed_at = datetime.utcnow()
                db.commit()
        finally:
            db.close()
        
        return {
            "analysis_id": analysis_id,
            "status": "completed",
            "mcp_score": 75.5
        }
        
    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        raise