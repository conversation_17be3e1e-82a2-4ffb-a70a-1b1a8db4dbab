services:
  # PostgreSQL Database
  db:
    image: postgres:15
    container_name: supermcp-db
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 30s
      timeout: 10s
      retries: 5
    restart: ${RESTART_POLICY:-no}
    networks:
      - supermcp-network

  # Redis for Celery and caching
  redis:
    image: redis:7-alpine
    container_name: supermcp-redis
    ports:
      - "${REDIS_PORT:-6379}:6379"
    command: redis-server --protected-mode no ${REDIS_AUTH_CMD:-}
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    restart: ${RESTART_POLICY:-no}
    networks:
      - supermcp-network

  # FastAPI Backend
  backend:
    build:
      context: ./backend
      dockerfile: ${BACKEND_DOCKERFILE:-Dockerfile}
    container_name: supermcp-backend
    environment:
      - ENVIRONMENT=${ENVIRONMENT:-development}
      - DEBUG=${DEBUG:-true}
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - CELERY_BROKER_URL=${CELERY_BROKER_URL}
      - CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND}
      - JWT_SECRET=${JWT_SECRET}
      - SECRET_KEY=${SECRET_KEY}
      - GITHUB_CLIENT_ID=${GITHUB_CLIENT_ID}
      - GITHUB_CLIENT_SECRET=${GITHUB_CLIENT_SECRET}
      - GITHUB_REDIRECT_URL=${GITHUB_REDIRECT_URL}
      - CORS_ORIGINS=${CORS_ORIGINS}
      - TAVILY_API_KEY=${TAVILY_API_KEY}
      - CONTEXT7_MCP_URL=${CONTEXT7_MCP_URL}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
    ports:
      - "${BACKEND_PORT:-8000}:8000"
    volumes:
      - ./backend:/app
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: ${RESTART_POLICY:-no}
    networks:
      - supermcp-network

  # Celery Worker
  celery-worker:
    build:
      context: ./backend
      dockerfile: ${BACKEND_DOCKERFILE:-Dockerfile}
    container_name: supermcp-celery-worker
    command: celery -A app.tasks.celery_app worker --loglevel=${CELERY_LOG_LEVEL:-info} --concurrency=${CELERY_CONCURRENCY:-2}
    environment:
      - ENVIRONMENT=${ENVIRONMENT:-development}
      - DEBUG=${DEBUG:-true}
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - CELERY_BROKER_URL=${CELERY_BROKER_URL}
      - CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND}
      - JWT_SECRET=${JWT_SECRET}
      - SECRET_KEY=${SECRET_KEY}
      - GITHUB_CLIENT_ID=${GITHUB_CLIENT_ID}
      - GITHUB_CLIENT_SECRET=${GITHUB_CLIENT_SECRET}
      - TAVILY_API_KEY=${TAVILY_API_KEY}
      - CONTEXT7_MCP_URL=${CONTEXT7_MCP_URL}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
    volumes:
      - ./backend:/app
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: ${RESTART_POLICY:-no}
    networks:
      - supermcp-network

  # Next.js Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: ${FRONTEND_DOCKERFILE:-Dockerfile}
    container_name: supermcp-frontend
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
      - NEXT_PUBLIC_APP_NAME=${NEXT_PUBLIC_APP_NAME}
    ports:
      - "${FRONTEND_PORT:-3000}:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      - backend
    restart: ${RESTART_POLICY:-no}
    networks:
      - supermcp-network

  # Nginx (only for production)
  nginx:
    image: nginx:alpine
    container_name: supermcp-nginx
    ports:
      - "${HTTP_PORT:-80}:80"
      - "${HTTPS_PORT:-443}:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - backend
      - frontend
    restart: ${RESTART_POLICY:-no}
    networks:
      - supermcp-network
    profiles:
      - production

  # Celery Beat (only for production)
  celery-beat:
    build:
      context: ./backend
      dockerfile: ${BACKEND_DOCKERFILE:-Dockerfile}
    container_name: supermcp-celery-beat
    command: celery -A app.tasks.celery_app beat --loglevel=${CELERY_LOG_LEVEL:-info}
    environment:
      - ENVIRONMENT=${ENVIRONMENT:-development}
      - DEBUG=${DEBUG:-true}
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - CELERY_BROKER_URL=${CELERY_BROKER_URL}
      - CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND}
      - JWT_SECRET=${JWT_SECRET}
      - SECRET_KEY=${SECRET_KEY}
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: ${RESTART_POLICY:-no}
    networks:
      - supermcp-network
    profiles:
      - production

  # Flower (Celery monitoring - optional)
  flower:
    build:
      context: ./backend
      dockerfile: ${BACKEND_DOCKERFILE:-Dockerfile}
    container_name: supermcp-flower
    command: celery -A app.tasks.celery_app flower --port=5555
    environment:
      - CELERY_BROKER_URL=${CELERY_BROKER_URL}
      - CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND}
      - FLOWER_BASIC_AUTH=${FLOWER_USER:-admin}:${FLOWER_PASSWORD:-admin}
    ports:
      - "${FLOWER_PORT:-5555}:5555"
    depends_on:
      redis:
        condition: service_healthy
    restart: ${RESTART_POLICY:-no}
    networks:
      - supermcp-network
    profiles:
      - monitoring

volumes:
  postgres_data:
  redis_data:

networks:
  supermcp-network:
    driver: bridge
