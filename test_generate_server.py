#!/usr/bin/env python3
"""
Test script to debug the Generate Server functionality
"""

import requests
import json
import sys

def test_generate_server():
    """Test the generate server endpoint with proper authentication"""
    
    base_url = "http://localhost:8000/api/v1"
    
    # First, let's check what analysis data we have
    print("🔍 Testing Generate Server Functionality")
    print("=" * 50)
    
    # Test without auth first to see the error
    print("\n1. Testing without authentication...")
    try:
        response = requests.post(f"{base_url}/mcp-generation/1/generate-server", 
                               json={
                                   "selected_tools": [
                                       {
                                           "tool_name": "strapi_run",
                                           "description": "Execute run business logic from strapi",
                                           "category": "business_logic"
                                       }
                                   ],
                                   "target_language": "python"
                               })
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test the suggestions endpoint (which seems to work)
    print("\n2. Testing suggestions endpoint...")
    try:
        response = requests.get(f"{base_url}/mcp-generation/1/suggestions")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Found {len(data.get('mcp_suggestions', {}).get('tools', []))} tools")
            for tool in data.get('mcp_suggestions', {}).get('tools', [])[:3]:
                print(f"  - {tool.get('name', 'unknown')}: {tool.get('description', '')[:50]}...")
        else:
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test analysis endpoint
    print("\n3. Testing analysis endpoint...")
    try:
        response = requests.get(f"{base_url}/analysis/1")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Repository: {data.get('repo_owner')}/{data.get('repo_name')}")
            print(f"Status: {data.get('status')}")
            if data.get('analysis_results'):
                results = data['analysis_results']
                print(f"Analysis keys: {list(results.keys())}")
                if 'repository_info' in results:
                    repo_info = results['repository_info']
                    print(f"Repository info: {repo_info}")
        else:
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"Error: {e}")
    
    print("\n4. Checking what's in the database...")
    # This would require database access, so let's skip for now
    
    print("\n📋 Summary:")
    print("- The issue appears to be authentication-related for the generate-server endpoint")
    print("- The suggestions endpoint works without auth (or has different auth requirements)")
    print("- The analysis data contains the correct repository info")
    print("- The frontend must be sending auth tokens for some endpoints but not others")

if __name__ == "__main__":
    test_generate_server()
