#!/bin/bash

# SuperMCP Development Environment Reset Script
# This script completely resets the development environment to a fresh state

set -e  # Exit on any error

echo "🧹 SuperMCP Development Environment Reset"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    print_error "docker-compose is not installed or not in PATH"
    exit 1
fi

# Step 1: Stop all services
print_status "Stopping all Docker services..."
docker-compose down --remove-orphans

# Step 2: Remove all containers, volumes, and networks
print_status "Removing all containers, volumes, and networks..."
docker-compose down --volumes --remove-orphans

# Step 3: Remove any dangling images (optional)
print_status "Cleaning up Docker images..."
docker image prune -f

# Step 4: Start services
print_status "Starting fresh Docker services..."
docker-compose up -d

# Step 5: Wait for database to be ready
print_status "Waiting for database to be ready..."
sleep 10

# Check if database is ready
max_attempts=30
attempt=1
while [ $attempt -le $max_attempts ]; do
    if docker-compose exec -T db pg_isready -U postgres -d supermcp > /dev/null 2>&1; then
        print_success "Database is ready!"
        break
    fi
    
    if [ $attempt -eq $max_attempts ]; then
        print_error "Database failed to start after $max_attempts attempts"
        exit 1
    fi
    
    print_status "Waiting for database... (attempt $attempt/$max_attempts)"
    sleep 2
    ((attempt++))
done

# Step 6: Run consolidated database migration
print_status "Running consolidated database migration..."
docker-compose exec -T backend alembic upgrade head

# Step 7: SuperMCP Beta System Ready
print_status "SuperMCP Beta system ready with consolidated schema..."
print_success "✅ Consolidated database schema - no more multiple migrations!"
print_success "✅ Repository-specific MCP tool extraction from actual code"
print_success "✅ Functional MCP server generation with working implementations"
print_success "✅ Complete deployment-ready MCP servers with documentation"
print_success "✅ Simplified workflow - focus on what matters"

# Step 8: Wait for backend to be ready
print_status "Waiting for backend to be ready..."
sleep 5

# Step 9: Verify enhanced features configuration
print_status "Verifying enhanced MCP features configuration..."

# Check if Claude API is configured
if docker-compose exec -T backend python -c "import os; exit(0 if os.getenv('ANTHROPIC_API_KEY') else 1)" 2>/dev/null; then
    print_success "Claude 3.5 Sonnet API configured"
else
    print_warning "Claude API not configured - will fallback to OpenAI"
fi

# Check if Tavily API is configured for web search
if docker-compose exec -T backend python -c "import os; exit(0 if os.getenv('TAVILY_API_KEY') else 1)" 2>/dev/null; then
    print_success "Tavily API configured for marketplace search"
else
    print_warning "Tavily API not configured - limited marketplace search"
fi

# Step 10: Final status check
print_status "Checking service status..."
sleep 5

# Check if all services are running
SERVICES=("db" "redis" "backend" "celery-worker" "frontend")
ALL_HEALTHY=true

for service in "${SERVICES[@]}"; do
    if docker-compose ps $service | grep -q "Up"; then
        print_success "$service is running"
    else
        print_error "$service is not running"
        ALL_HEALTHY=false
    fi
done

# Final summary
echo ""
echo "🎉 Development Environment Reset Complete!"
echo "=========================================="

if [ "$ALL_HEALTHY" = true ]; then
    print_success "All services are running successfully"
    echo ""
    echo "📋 Next Steps:"
    echo "1. Visit: http://localhost:3000"
    echo "2. Sign up/login with your GitHub account"
    echo "3. Start analyzing repositories!"
    echo ""
    echo "🚀 Enhanced Features Available:"
    echo "   ✅ Repository-Specific MCP Tool Extraction"
    echo "   ✅ Functional MCP Server Generation (No Templates!)"
    echo "   ✅ Working Code Based on Actual Repository Analysis"
    echo "   ✅ Complete Deployment-Ready MCP Servers"
    echo "   ✅ Enhanced Database Schema for Tool Intelligence"
    echo "   ✅ AI-Powered Code Understanding and Generation"
    echo ""
    echo "🔗 Available URLs:"
    echo "   Frontend: http://localhost:3000"
    echo "   Backend API: http://localhost:8000"
    echo "   API Docs: http://localhost:8000/docs"
    echo ""
    echo "🎯 Key Improvements:"
    echo "   • Repository-specific tools extracted from actual functions and APIs"
    echo "   • Functional MCP servers with working implementations (no templates)"
    echo "   • Enhanced database schema storing detailed tool information"
    echo "   • Complete deployment packages with Docker and documentation"
    echo "   • Intelligent tool categorization and priority scoring"
    echo "   • End-to-end workflow from analysis to production-ready servers"
else
    print_error "Some services failed to start. Check the logs:"
    echo "   docker-compose logs"
fi

echo ""
echo "🔧 Environment Configuration:"
echo "=========================================="
echo "For optimal performance, ensure these environment variables are set:"
echo ""
echo "Required:"
echo "  GITHUB_CLIENT_ID=your_github_client_id"
echo "  GITHUB_CLIENT_SECRET=your_github_client_secret"
echo "  OPENAI_API_KEY=your_openai_api_key"
echo ""
echo "Enhanced Features (Recommended):"
echo "  ANTHROPIC_API_KEY=your_claude_api_key    # For superior code analysis"
echo "  TAVILY_API_KEY=your_tavily_api_key       # For marketplace search"
echo "  CONTEXT7_MCP_URL=your_context7_url       # For documentation access"
echo ""
echo "💡 With all APIs configured, you get:"
echo "   • Enhanced repository analysis and code understanding"
echo "   • More accurate tool extraction from complex codebases"
echo "   • Better business logic identification and mapping"
echo "   • Superior functional code generation quality"
echo ""
echo "🚀 Enhanced MCP Generation Features:"
echo "   • Repository-specific tools based on actual code analysis"
echo "   • Functional implementations instead of generic templates"
echo "   • Complete MCP servers ready for immediate deployment"
echo "   • Working code that integrates with your existing systems"
echo "   • Enhanced database schema for intelligent tool management"
echo "   • Example: 'supermcp_analyze_repository_api' instead of 'generic-api-mcp'"
